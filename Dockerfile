FROM python:3.11

# 设置工作目录
WORKDIR /aitest

# 更新源并安装依赖
RUN echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm          main contrib non-free non-free-firmware" > /etc/apt/sources.list && \
    echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm-updates main contrib non-free non-free-firmware" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm-backports main contrib non-free non-free-firmware" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian-security bookworm-security main contrib non-free non-free-firmware" >> /etc/apt/sources.list


# RUN rm /etc/apt/sources.list.d/* && \
#     apt update && \
#     apt install -y pandoc

# 安装playwright及其依赖
# RUN pip install playwright -i https://mirrors.aliyun.com/pypi/simple/ && \
#     playwright install-deps && \
#     playwright install chromium

# 复制依赖文件并安装Python依赖
COPY requirements.txt .
RUN pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

# 复制项目源文件到镜像内
COPY .env /aitest/
COPY src/ /aitest/src/
COPY prompts/ /aitest/prompts/
COPY static/ /aitest/static/
COPY templates/ /aitest/templates/
COPY app.py /aitest/

# 创建data目录并设置卷映射
RUN mkdir -p /aitest/data
#VOLUME ["/aitest/data", "/aitest/.env"]

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["python", "app.py"]