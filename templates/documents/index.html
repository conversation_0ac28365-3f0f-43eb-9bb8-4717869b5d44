{% extends "base.html" %}

{% block title %}文档管理 - AI智能测试平台{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/aitest">首页</a></li>
<li class="breadcrumb-item active">文档管理</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-file-alt me-2"></i>文档管理
            </h1>
            <button class="btn btn-primary" onclick="showUploadModal()" id="uploadBtn" disabled>
                <i class="fas fa-upload me-1"></i>上传文档
            </button>
        </div>
    </div>
</div>

<!-- 项目选择提示 -->
<div class="row" id="projectPrompt">
    <div class="col-12">
        <div class="alert alert-warning" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            请先在顶部导航栏选择一个项目
        </div>
    </div>
</div>

<!-- 文档列表 -->
<div class="row" id="documentsSection" style="display: none;">
    <div class="col-12">
        <!-- 搜索和过滤 -->
        <div class="row mb-4">
            <div class="col-md-5">
                <div class="search-box">
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索文档名称或内容...">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="pageSizeSelect" onchange="changePageSize()">
                    <option value="10" selected>10条</option>
                    <option value="30">30条</option>
                    <option value="100">100条</option>
                </select>
            </div>
            <div class="col-md-5 text-end">
                <button class="btn btn-outline-secondary" onclick="refreshDocuments()">
                    <i class="fas fa-sync me-1"></i>刷新
                </button>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>文档名称</th>
                                <th>文件类型</th>
                                <th>文件大小</th>
                                <th>上传时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="documentsTableBody">
                            <!-- 文档列表将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div id="pagination"></div>
            </div>
        </div>
    </div>
</div>

<!-- 上传文档模态框 -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">上传文档</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="documentFile" class="form-label">选择文件 <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="documentFile" accept=".pdf,.docx,.doc,.md,.txt" required>
                        <div class="form-text">支持格式: PDF, Word, Markdown, 文本文件</div>
                    </div>
                    <div class="mb-3">
                        <label for="documentName" class="form-label">文档名称</label>
                        <input type="text" class="form-control" id="documentName" placeholder="留空则使用文件名">
                    </div>
                    <div class="mb-3">
                        <label for="documentDescription" class="form-label">文档描述</label>
                        <textarea class="form-control" id="documentDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="uploadDocument()">上传</button>
            </div>
        </div>
    </div>
</div>

<!-- 文档预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewTitle">文档预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent" style="max-height: 600px; overflow-y: auto;">
                    <!-- 预览内容 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="extractRequirementsBtn" onclick="extractRequirements()">
                    <i class="fas fa-magic me-1"></i>提取需求
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let currentSearch = '';
let currentDocumentId = null;

$(document).ready(function() {
    checkCurrentProject();
    
    // 搜索输入框事件
    $('#searchInput').on('input', debounce(function() {
        currentSearch = $(this).val();
        currentPage = 1;
        loadDocuments();
    }, 500));

    // 搜索输入框回车键事件
    $('#searchInput').on('keypress', function(e) {
        if (e.which === 13) { // 回车键
            currentSearch = $(this).val();
            currentPage = 1;
            loadDocuments();
        }
    });
    
    // 文件选择事件
    $('#documentFile').change(function() {
        const file = this.files[0];
        if (file && !$('#documentName').val()) {
            const fileName = file.name.replace(/\.[^/.]+$/, "");
            $('#documentName').val(fileName);
        }
    });
});

// 检查当前项目
function checkCurrentProject() {
    $.get('/aitest/api/current_project', function(response) {
        if (response.project_id) {
            $('#projectPrompt').hide();
            $('#documentsSection').show();
            $('#uploadBtn').prop('disabled', false);
            loadDocuments();
        } else {
            $('#projectPrompt').show();
            $('#documentsSection').hide();
            $('#uploadBtn').prop('disabled', true);
        }
    });
}

// 加载文档列表
function loadDocuments() {
    const pageSize = parseInt($('#pageSizeSelect').val()) || 10;
    const params = {
        page: currentPage,
        page_size: pageSize
    };
    
    if (currentSearch) {
        params.search = currentSearch;
    }
    
    $.get('/aitest/documents/api/list', params, function(response) {
        if (response.success) {
            renderDocumentsTable(response.data.records);
            initPagination('pagination', response.data.total_pages, currentPage, function(page) {
                currentPage = page;
                loadDocuments();
            });
        } else {
            showNotification('加载文档列表失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('加载文档列表失败', 'error');
    });
}

// 渲染文档表格
function renderDocumentsTable(documents) {
    const tbody = $('#documentsTableBody');
    tbody.empty();
    
    if (documents.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="5" class="text-center text-muted py-4">
                    <i class="fas fa-file fa-2x mb-2"></i><br>
                    暂无文档数据
                </td>
            </tr>
        `);
        return;
    }
    
    documents.forEach(function(doc) {
        const row = $(`
            <tr>
                <td>
                    <strong>${doc.name}</strong>
                    ${doc.description ? '<br><small class="text-muted">' + doc.description + '</small>' : ''}
                </td>
                <td>
                    <span class="badge bg-secondary">${doc.file_type || 'unknown'}</span>
                </td>
                <td>
                    <small>${formatFileSize(doc.file_size || 0)}</small>
                </td>
                <td>
                    <small class="text-muted">${formatDate(doc.created_at)}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="previewDocument('${doc.id}')" title="预览">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="extractRequirements('${doc.id}')" title="提取需求">
                            <i class="fas fa-magic"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteDocument('${doc.id}', '${doc.name}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);
        tbody.append(row);
    });
}

// 显示上传模态框
function showUploadModal() {
    $('#uploadForm')[0].reset();
    const modal = new bootstrap.Modal($('#uploadModal')[0]);
    modal.show();
}

// 上传文档
function uploadDocument() {
    const fileInput = $('#documentFile')[0];
    const file = fileInput.files[0];

    if (!file) {
        showNotification('请选择文件', 'error');
        return;
    }

    const formData = new FormData();
    formData.append('file', file);
    formData.append('name', $('#documentName').val().trim());
    formData.append('description', $('#documentDescription').val().trim());

    showProgress('正在上传文档...');

    $.ajax({
        url: '/aitest/documents/api/upload',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        timeout: 300000, // 5分钟超时
        success: function(response) {
            hideProgress();
            if (response && response.success) {
                showNotification(response.message || '文档上传成功', 'success');
                bootstrap.Modal.getInstance($('#uploadModal')[0]).hide();
                loadDocuments();
                // 重置表单
                $('#uploadForm')[0].reset();
            } else {
                showNotification(response.message || '上传失败', 'error');
            }
        },
        error: function(xhr, status, error) {
            hideProgress();
            console.error('上传错误:', xhr, status, error);

            let errorMessage = '上传文档失败';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (status === 'timeout') {
                errorMessage = '上传超时，请检查网络连接或文件大小';
            } else if (status === 'error') {
                if (xhr.status === 0) {
                    errorMessage = '网络连接失败，请检查网络';
                } else if (xhr.status === 413) {
                    errorMessage = '文件过大，请选择较小的文件';
                } else if (xhr.status >= 500) {
                    errorMessage = '服务器错误，请稍后重试';
                } else if (xhr.status >= 400) {
                    errorMessage = '请求错误: ' + (xhr.responseJSON?.message || xhr.statusText);
                }
            }

            showNotification(errorMessage, 'error');
        },
        complete: function() {
            // 确保进度框被隐藏
            setTimeout(hideProgress, 100);
        }
    });
}

// 预览文档
function previewDocument(documentId) {
    currentDocumentId = documentId;
    
    // 获取文档信息
    $.get(`/aitest/documents/api/${documentId}`, function(response) {
        if (response.success) {
            $('#previewTitle').text(response.data.name);
            $('#extractRequirementsBtn').attr('onclick', `extractRequirements('${documentId}')`);
        }
    });
    
    // 获取预览内容
    showProgress('正在加载预览...');

    $.get(`/aitest/documents/api/${documentId}/preview`, function(html) {
        hideProgress();
        $('#previewContent').html(html);
        const modal = new bootstrap.Modal($('#previewModal')[0]);
        modal.show();
    }).fail(function(xhr, status, error) {
        hideProgress();
        console.error('预览加载错误:', xhr, status, error);

        let errorMessage = '加载预览失败';
        if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage = xhr.responseJSON.message;
        } else if (xhr.status >= 500) {
            errorMessage = '服务器错误，请稍后重试';
        } else if (xhr.status === 404) {
            errorMessage = '文档不存在或已被删除';
        }

        showNotification(errorMessage, 'error');
    }).always(function() {
        // 确保进度框被隐藏
        setTimeout(hideProgress, 100);
    });
}

// 提取需求
function extractRequirements(documentId) {
    if (!documentId && currentDocumentId) {
        documentId = currentDocumentId;
    }
    
    if (!documentId) {
        showNotification('请选择文档', 'error');
        return;
    }
    
    // 跳转到需求管理页面并传递文档ID
    window.location.href = `/aitest/requirements?extract_from=${documentId}`;
}

// 删除文档
function deleteDocument(documentId, documentName) {
    if (confirm(`确定要删除文档"${documentName}"吗？`)) {
        $.ajax({
            url: `/aitest/documents/api/${documentId}/delete`,
            method: 'DELETE',
            success: function(response) {
                if (response.success) {
                    showNotification(response.message, 'success');
                    loadDocuments();
                } else {
                    showNotification(response.message, 'error');
                }
            },
            error: function() {
                showNotification('删除文档失败', 'error');
            }
        });
    }
}

// 刷新文档列表
function refreshDocuments() {
    currentPage = 1;
    currentSearch = '';
    $('#searchInput').val('');
    loadDocuments();
}

// 刷新页面数据
function refreshPageData() {
    checkCurrentProject();
}

// 改变分页大小
function changePageSize() {
    currentPage = 1; // 重置到第一页
    loadDocuments();
}
</script>
{% endblock %}
