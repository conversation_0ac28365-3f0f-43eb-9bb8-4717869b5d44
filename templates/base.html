<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AI智能测试平台{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="/aitest/static/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="/aitest/static/css/all.min.css" rel="stylesheet">
    <!--<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">-->
    <!-- Custom CSS -->
    <link href="/aitest/static/css/style.css" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-robot me-2"></i>AI智能测试平台
            </a>
            
            <!-- 项目选择器 -->
            <div class="navbar-nav me-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="projectDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-project-diagram me-1"></i>
                        <span id="currentProjectName">选择项目</span>
                    </a>
                    <ul class="dropdown-menu" id="projectDropdownMenu">
                        <li><a class="dropdown-item" href="#" onclick="loadProjects()">
                            <i class="fas fa-sync me-2"></i>刷新项目列表
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/aitest/projects/">
                            <i class="fas fa-plus me-2"></i>管理项目
                        </a></li>
                    </ul>
                </div>
            </div>
            
            <!-- 右侧菜单 
            <div class="navbar-nav">
                <a class="nav-link" href="/aitest/system/">
                    <i class="fas fa-cog me-1"></i>系统配置
                </a>
            </div>-->
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse" id="sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/aitest">
                                <i class="fas fa-home me-2"></i>首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/aitest/projects/">
                                <i class="fas fa-project-diagram me-2"></i>项目管理
                            </a>
                        </li>
                        
                        <!-- 知识库管理 -->
                        <li class="nav-item">
                            <a class="nav-link" href="/aitest/knowledge/">
                                <i class="fas fa-database me-2"></i>知识库管理
                            </a>
                        </li>
                        
                        <!-- 需求管理 -->
                        <li class="nav-item">
                            <a class="nav-link collapsed" data-bs-toggle="collapse" href="#requirementSubmenu">
                                <i class="fas fa-file-alt me-2"></i>需求管理
                                <i class="fas fa-chevron-down ms-auto"></i>
                            </a>
                            <div class="collapse" id="requirementSubmenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="/aitest/documents/">
                                            <i class="fas fa-file me-2"></i>文档管理
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="/aitest/requirements/">
                                            <i class="fas fa-list me-2"></i>功能点管理
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        
                        <!-- 测试管理 -->
                        <li class="nav-item">
                            <a class="nav-link collapsed" data-bs-toggle="collapse" href="#testSubmenu">
                                <i class="fas fa-vial me-2"></i>测试管理
                                <i class="fas fa-chevron-down ms-auto"></i>
                            </a>
                            <div class="collapse" id="testSubmenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="/aitest/tests/usercase/">
                                            <i class="fas fa-list me-2"></i>用户测试用例
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="/aitest/tests/automation/">
                                            <i class="fas fa-robot me-2"></i>自动化测试用例
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        
                        
                        <!-- 系统管理 -->
                        <li class="nav-item">
                            <a class="nav-link collapsed" data-bs-toggle="collapse" href="#systemSubmenu">
                                <i class="fas fa-vial me-2"></i>系统管理
                                <i class="fas fa-chevron-down ms-auto"></i>
                            </a>
                            <div class="collapse" id="systemSubmenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="/aitest/system/">
                                            <i class="fas fa-list me-2"></i>系统配置
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="/aitest/tasks/">
                                            <i class="fas fa-robot me-2"></i>任务管理
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                    </ul>
                    
                    <!-- 侧边栏折叠按钮 -->
                    <div class="mt-3">
                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- 面包屑导航 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            {% block breadcrumb %}
                            <li class="breadcrumb-item"><a href="/aitest">首页</a></li>
                            {% endblock %}
                        </ol>
                    </nav>
                </div>

                <!-- 页面内容 -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- 进度提示模态框 -->
    <div class="modal fade" id="progressModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p id="progressMessage">正在处理，请稍候...</p>
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%" id="progressBar"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知提示 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="notificationToast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-info-circle text-primary me-2"></i>
                <strong class="me-auto">提示</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="notificationMessage">
                消息内容
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/aitest/static/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="/aitest/static/js/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="/aitest/static/js/common.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
