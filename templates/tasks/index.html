{% extends "base.html" %}

{% block title %}任务管理{% endblock %}

{% block extra_css %}
<style>
.task-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}
.task-status.pending { background-color: #fef3c7; color: #92400e; }
.task-status.running { background-color: #dbeafe; color: #1e40af; }
.task-status.completed { background-color: #d1fae5; color: #065f46; }
.task-status.failed { background-color: #fee2e2; color: #991b1b; }
.task-status.cancelled { background-color: #f3f4f6; color: #374151; }

.progress-bar {
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}
.progress-fill {
    height: 100%;
    background-color: #3b82f6;
    transition: width 0.3s ease;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-tasks me-2"></i>任务管理
            </h1>
            <div class="btn-group">
                <button class="btn btn-outline-secondary" onclick="refreshTasks()">
                    <i class="fas fa-sync-alt me-1"></i>刷新
                </button>
                <button class="btn btn-outline-danger" onclick="cleanupTasks()">
                    <i class="fas fa-trash me-1"></i>清理已完成
                </button>
            </div>
        </div>

        <!-- 任务过滤器 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <select class="form-select" id="taskTypeFilter" onchange="loadTasks()">
                    <option value="">全部类型</option>
                    <option value="generate_test_cases">生成测试用例</option>
                    <option value="generate_automation_test_cases">生成自动化测试用例</option>
                    <option value="extract_requirements">提取需求功能点</option>
                    <option value="auto_import_documents">导入文档到知识库</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="taskStatusFilter" onchange="loadTasks()">
                    <option value="">全部状态</option>
                    <option value="pending">等待中</option>
                    <option value="running">运行中</option>
                    <option value="completed">已完成</option>
                    <option value="failed">失败</option>
                    <option value="cancelled">已取消</option>
                </select>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>任务名称</th>
                                <th>类型</th>
                                <th>状态</th>
                                <th>进度</th>
                                <th>创建时间</th>
                                <th>完成时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="tasksTableBody">
                            <!-- 任务列表将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 任务详情模态框 -->
<div class="modal fade" id="taskDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">任务详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="taskDetailContent">
                    <!-- 任务详情内容将通过JavaScript动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-danger" id="cancelTaskBtn" onclick="cancelCurrentTask()" style="display: none;">取消任务</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentTaskId = null;
let refreshInterval = null;

$(document).ready(function() {
    loadTasks();
    
    // 每5秒自动刷新任务列表
    refreshInterval = setInterval(loadTasks, 5000);
});

// 页面卸载时清理定时器
$(window).on('beforeunload', function() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
});

function loadTasks() {
    const taskType = $('#taskTypeFilter').val();
    const status = $('#taskStatusFilter').val();
    
    let url = '/aitest/api/tasks/list';
    const params = [];
    if (taskType) params.push(`task_type=${encodeURIComponent(taskType)}`);
    if (status) params.push(`status=${encodeURIComponent(status)}`);
    if (params.length > 0) url += '?' + params.join('&');
    
    $.ajax({
        url: url,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                renderTasks(response.data);
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function() {
            showNotification('获取任务列表失败', 'error');
        }
    });
}

function renderTasks(tasks) {
    const tbody = $('#tasksTableBody');
    tbody.empty();
    
    if (tasks.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="fas fa-tasks fa-2x mb-2"></i><br>
                    暂无任务数据
                </td>
            </tr>
        `);
        return;
    }
    
    tasks.forEach(task => {
        const statusClass = task.status;
        const statusText = getStatusText(task.status);
        const createdAt = new Date(task.created_at).toLocaleString();
        const completedAt = task.completed_at ? new Date(task.completed_at).toLocaleString() : '-';
        
        const row = `
            <tr>
                <td>
                    <div class="fw-medium">${escapeHtml(task.task_name)}</div>
                    <small class="text-muted">${task.task_id}</small>
                </td>
                <td>${getTaskTypeText(task.task_type)}</td>
                <td><span class="task-status ${statusClass}">${statusText}</span></td>
                <td>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${task.progress}%"></div>
                    </div>
                    <small class="text-muted">${task.progress}%</small>
                </td>
                <td>${createdAt}</td>
                <td>${completedAt}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="showTaskDetail('${task.task_id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${task.status === 'running' || task.status === 'pending' ? 
                        `<button class="btn btn-sm btn-outline-danger ms-1" onclick="cancelTask('${task.task_id}')">
                            <i class="fas fa-times"></i>
                        </button>` : ''}
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function getStatusText(status) {
    const statusMap = {
        'pending': '等待中',
        'running': '运行中',
        'completed': '已完成',
        'failed': '失败',
        'cancelled': '已取消'
    };
    return statusMap[status] || status;
}

function getTaskTypeText(taskType) {
    const typeMap = {
        'generate_test_cases': '生成测试用例',
        'generate_automation_test_cases': '生成自动化测试用例',
        'extract_requirements': '提取需求功能点',
        'auto_import_documents': '导入文档到知识库'
    };
    return typeMap[taskType] || taskType;
}

function showTaskDetail(taskId) {
    currentTaskId = taskId;
    
    $.ajax({
        url: `/aitest/api/tasks/status/${taskId}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                renderTaskDetail(response.data);
                $('#taskDetailModal').modal('show');
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function() {
            showNotification('获取任务详情失败', 'error');
        }
    });
}

function renderTaskDetail(task) {
    const statusText = getStatusText(task.status);
    const typeText = getTaskTypeText(task.task_type);
    const createdAt = new Date(task.created_at).toLocaleString();
    const startedAt = task.started_at ? new Date(task.started_at).toLocaleString() : '-';
    const completedAt = task.completed_at ? new Date(task.completed_at).toLocaleString() : '-';
    
    let content = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td>任务ID:</td><td>${task.task_id}</td></tr>
                    <tr><td>任务名称:</td><td>${escapeHtml(task.task_name)}</td></tr>
                    <tr><td>任务类型:</td><td>${typeText}</td></tr>
                    <tr><td>状态:</td><td><span class="task-status ${task.status}">${statusText}</span></td></tr>
                    <tr><td>进度:</td><td>${task.progress}%</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>时间信息</h6>
                <table class="table table-sm">
                    <tr><td>创建时间:</td><td>${createdAt}</td></tr>
                    <tr><td>开始时间:</td><td>${startedAt}</td></tr>
                    <tr><td>完成时间:</td><td>${completedAt}</td></tr>
                </table>
            </div>
        </div>
    `;
    
    if (task.error) {
        content += `
            <div class="mt-3">
                <h6>错误信息</h6>
                <div class="alert alert-danger">
                    ${escapeHtml(task.error)}
                </div>
            </div>
        `;
    }
    
    if (task.result) {
        content += `
            <div class="mt-3">
                <h6>执行结果</h6>
                <pre class="bg-light p-3 rounded">${JSON.stringify(task.result, null, 2)}</pre>
            </div>
        `;
    }
    
    $('#taskDetailContent').html(content);
    
    // 显示/隐藏取消按钮
    if (task.status === 'running' || task.status === 'pending') {
        $('#cancelTaskBtn').show();
    } else {
        $('#cancelTaskBtn').hide();
    }
}

function cancelTask(taskId) {
    if (confirm('确定要取消这个任务吗？')) {
        $.ajax({
            url: `/aitest/api/tasks/cancel/${taskId}`,
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    showNotification(response.message, 'success');
                    loadTasks();
                } else {
                    showNotification(response.message, 'error');
                }
            },
            error: function() {
                showNotification('取消任务失败', 'error');
            }
        });
    }
}

function cancelCurrentTask() {
    if (currentTaskId) {
        cancelTask(currentTaskId);
        $('#taskDetailModal').modal('hide');
    }
}

function refreshTasks() {
    loadTasks();
    showNotification('任务列表已刷新', 'success');
}

function cleanupTasks() {
    if (confirm('确定要清理所有已完成的任务吗？')) {
        $.ajax({
            url: '/aitest/api/tasks/cleanup',
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    showNotification(response.message, 'success');
                    loadTasks();
                } else {
                    showNotification(response.message, 'error');
                }
            },
            error: function() {
                showNotification('清理任务失败', 'error');
            }
        });
    }
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
</script>
{% endblock %}
