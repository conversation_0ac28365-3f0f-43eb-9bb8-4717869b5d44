{% extends "base.html" %}

{% block title %}系统配置 - AI智能测试平台{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/aitest">首页</a></li>
<li class="breadcrumb-item active">系统配置</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-cog me-2"></i>系统配置
            </h1>
            <button class="btn btn-primary" onclick="saveConfig()">
                <i class="fas fa-save me-1"></i>保存配置
            </button>
        </div>
    </div>
</div>

<!-- 配置表单 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form id="configForm">
                    <!-- LLM配置 -->
                    <div class="mb-4">
                        <h5 class="card-title">
                            <i class="fas fa-robot me-2"></i>大语言模型配置
                        </h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="openaiApiBase" class="form-label">API Base URL</label>
                                    <input type="text" class="form-control" id="openaiApiBase" placeholder="https://api.openai.com/v1">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="openaiApiKey" class="form-label">API Key</label>
                                    <input type="password" class="form-control" id="openaiApiKey" placeholder="sk-...">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="openaiModel" class="form-label">模型名称</label>
                                    <input type="text" class="form-control" id="openaiModel" placeholder="gpt-3.5-turbo">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="maxThreads" class="form-label">最大线程数</label>
                                    <input type="number" class="form-control" id="maxThreads" min="1" max="20" value="4">
                                    <div class="form-text">用于并发处理任务的最大线程数</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 嵌入模型配置 -->
                    <div class="mb-4">
                        <h5 class="card-title">
                            <i class="fas fa-vector-square me-2"></i>嵌入模型配置
                        </h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="embeddingApiBase" class="form-label">Embedding API Base URL</label>
                                    <input type="text" class="form-control" id="embeddingApiBase" placeholder="https://api.openai.com/v1">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="embeddingApiKey" class="form-label">Embedding API Key</label>
                                    <input type="password" class="form-control" id="embeddingApiKey" placeholder="sk-...">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="embeddingModel" class="form-label">嵌入模型名称</label>
                                    <input type="text" class="form-control" id="embeddingModel" placeholder="text-embedding-ada-002">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 知识库配置 -->
                    <div class="mb-4">
                        <h5 class="card-title">
                            <i class="fas fa-database me-2"></i>知识库配置
                        </h5>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="chunkSize" class="form-label">文本块大小</label>
                                    <input type="number" class="form-control" id="chunkSize" min="100" max="5000" value="1000">
                                    <div class="form-text">每个文本块的字符数</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="chunkOverlap" class="form-label">文本块重叠</label>
                                    <input type="number" class="form-control" id="chunkOverlap" min="0" max="1000" value="200">
                                    <div class="form-text">相邻文本块的重叠字符数</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="markdownSeparator" class="form-label">Markdown分隔符</label>
                                    <input type="text" class="form-control" id="markdownSeparator" value="###">
                                    <div class="form-text">用于按章节切分文档的分隔符</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 文件上传配置 -->
                    <div class="mb-4">
                        <h5 class="card-title">
                            <i class="fas fa-upload me-2"></i>文件上传配置
                        </h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="maxContentLength" class="form-label">最大文件大小 (MB)</label>
                                    <input type="number" class="form-control" id="maxContentLength" min="1" max="100" value="16">
                                    <div class="form-text">单个文件的最大上传大小</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 测试连接 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-plug me-2"></i>连接测试
                </h5>
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-outline-primary" onclick="testLLMConnection()">
                            <i class="fas fa-robot me-1"></i>测试LLM连接
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-outline-secondary" onclick="testEmbeddingConnection()">
                            <i class="fas fa-vector-square me-1"></i>测试嵌入模型连接
                        </button>
                    </div>
                </div>
                <div id="testResults" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<!-- 提示词管理 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-file-alt me-2"></i>提示词管理
                </h5>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>文件名</th>
                                <th>名称</th>
                                <th>大小</th>
                                <th>修改时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="promptsTableBody">
                            <!-- 提示词文件列表将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑提示词模态框 -->
<div class="modal fade" id="promptModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="promptModalTitle">编辑提示词</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="promptForm">
                    <input type="hidden" id="promptFilename">
                    <div class="mb-3">
                        <label for="promptContent" class="form-label">提示词内容</label>
                        <textarea class="form-control" id="promptContent" rows="15" placeholder="输入提示词内容..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="savePrompt()">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    loadConfig();
    loadPrompts();
});

// 加载配置
function loadConfig() {
    $.get('/aitest/system/api/config', function(response) {
        if (response.success) {
            const config = response.data;
            $('#openaiApiBase').val(config.OPENAI_API_BASE || '');
            $('#openaiApiKey').val(config.OPENAI_API_KEY || '');
            $('#openaiModel').val(config.OPENAI_MODEL || 'gpt-3.5-turbo');
            $('#maxThreads').val(config.MAX_THREADS || 4);
            $('#embeddingApiBase').val(config.EMBEDDING_API_BASE || '');
            $('#embeddingApiKey').val(config.EMBEDDING_API_KEY || '');
            $('#embeddingModel').val(config.EMBEDDING_MODEL || 'text-embedding-ada-002');
            $('#chunkSize').val(config.CHUNK_SIZE || 1000);
            $('#chunkOverlap').val(config.CHUNK_OVERLAP || 200);
            $('#markdownSeparator').val(config.MARKDOWN_SEPARATOR || '###');
            $('#maxContentLength').val((config.MAX_CONTENT_LENGTH || 16777216) / 1024 / 1024);
        } else {
            showNotification('加载配置失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('加载配置失败', 'error');
    });
}

// 保存配置
function saveConfig() {
    const config = {
        OPENAI_API_BASE: $('#openaiApiBase').val(),
        OPENAI_API_KEY: $('#openaiApiKey').val(),
        OPENAI_MODEL: $('#openaiModel').val(),
        MAX_THREADS: parseInt($('#maxThreads').val()) || 4,
        EMBEDDING_API_BASE: $('#embeddingApiBase').val(),
        EMBEDDING_API_KEY: $('#embeddingApiKey').val(),
        EMBEDDING_MODEL: $('#embeddingModel').val(),
        CHUNK_SIZE: parseInt($('#chunkSize').val()) || 1000,
        CHUNK_OVERLAP: parseInt($('#chunkOverlap').val()) || 200,
        MARKDOWN_SEPARATOR: $('#markdownSeparator').val() || '###',
        MAX_CONTENT_LENGTH: (parseInt($('#maxContentLength').val()) || 16) * 1024 * 1024
    };

    showProgress('正在保存配置...');

    $.ajax({
        url: '/aitest/system/api/config',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(config),
        success: function(response) {
            hideProgress();
            if (response.success) {
                showNotification('配置保存成功', 'success');
            } else {
                showNotification('保存配置失败: ' + response.message, 'error');
            }
        },
        error: function() {
            hideProgress();
            showNotification('保存配置失败', 'error');
        }
    });
}

// 测试LLM连接
function testLLMConnection() {
    showProgress('正在测试LLM连接...');

    $.post('/aitest/system/api/test_llm', function(response) {
        hideProgress();
        const resultDiv = $('#testResults');
        if (response.success) {
            resultDiv.html(`
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>LLM连接测试成功
                    <br><small>响应时间: ${response.data.response_time}ms</small>
                </div>
            `);
        } else {
            resultDiv.html(`
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle me-2"></i>LLM连接测试失败: ${response.message}
                </div>
            `);
        }
    }).fail(function() {
        hideProgress();
        $('#testResults').html(`
            <div class="alert alert-danger">
                <i class="fas fa-times-circle me-2"></i>LLM连接测试失败
            </div>
        `);
    });
}

// 测试嵌入模型连接
function testEmbeddingConnection() {
    showProgress('正在测试嵌入模型连接...');

    $.post('/aitest/system/api/test_embedding', function(response) {
        hideProgress();
        const resultDiv = $('#testResults');
        if (response.success) {
            resultDiv.html(`
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>嵌入模型连接测试成功
                    <br><small>响应时间: ${response.data.response_time}ms</small>
                </div>
            `);
        } else {
            resultDiv.html(`
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle me-2"></i>嵌入模型连接测试失败: ${response.message}
                </div>
            `);
        }
    }).fail(function() {
        hideProgress();
        $('#testResults').html(`
            <div class="alert alert-danger">
                <i class="fas fa-times-circle me-2"></i>嵌入模型连接测试失败
            </div>
        `);
    });
}

// 加载提示词列表
function loadPrompts() {
    $.get('/aitest/system/api/prompts', function(response) {
        if (response.success) {
            renderPromptsTable(response.data);
        } else {
            showNotification('加载提示词列表失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('加载提示词列表失败', 'error');
    });
}

// 渲染提示词表格
function renderPromptsTable(prompts) {
    const tbody = $('#promptsTableBody');
    tbody.empty();

    if (prompts.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="5" class="text-center text-muted py-4">
                    <i class="fas fa-file-alt fa-2x mb-2"></i><br>
                    暂无提示词文件
                </td>
            </tr>
        `);
        return;
    }

    prompts.forEach(function(prompt) {
        const modifiedDate = new Date(prompt.modified_at * 1000).toLocaleString();
        const fileSize = (prompt.size / 1024).toFixed(2) + ' KB';

        const row = $(`
            <tr>
                <td><code>${prompt.filename}</code></td>
                <td><strong>${prompt.name}</strong></td>
                <td>${fileSize}</td>
                <td><small class="text-muted">${modifiedDate}</small></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editPrompt('${prompt.filename}', '${prompt.name}')" title="编辑">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                </td>
            </tr>
        `);
        tbody.append(row);
    });
}

// 编辑提示词
function editPrompt(filename, name) {
    $('#promptModalTitle').text(`编辑提示词 - ${name}`);
    $('#promptFilename').val(filename);

    //showProgress('正在加载提示词内容...');

    $.get(`/aitest/system/api/prompts/${filename}`, function(response) {
        hideProgress();
        if (response.success) {
            $('#promptContent').val(response.data.content);
            const modal = new bootstrap.Modal($('#promptModal')[0]);
            modal.show();
        } else {
            showNotification('加载提示词内容失败: ' + response.message, 'error');
        }
    }).fail(function() {
        hideProgress();
        showNotification('加载提示词内容失败', 'error');
    });
}

// 保存提示词
function savePrompt() {
    const filename = $('#promptFilename').val();
    const content = $('#promptContent').val();

    if (!content.trim()) {
        showNotification('提示词内容不能为空', 'error');
        return;
    }

    showProgress('正在保存提示词...');

    $.ajax({
        url: `/aitest/system/api/prompts/${filename}`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify({content: content}),
        success: function(response) {
            hideProgress();
            if (response.success) {
                showNotification(response.message, 'success');
                bootstrap.Modal.getInstance($('#promptModal')[0]).hide();
                loadPrompts();
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            hideProgress();
            let errorMessage = '保存提示词失败';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            showNotification(errorMessage, 'error');
        }
    });
}
</script>
{% endblock %}