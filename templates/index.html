{% extends "base.html" %}

{% block title %}首页 - AI智能测试平台{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">首页</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-home me-2"></i>欢迎使用AI智能测试平台
        </h1>
    </div>
</div>

<!-- 项目选择提示 -->
<div class="row" id="projectPrompt" style="display: none;">
    <div class="col-12">
        <div class="alert alert-info" role="alert">
            <i class="fas fa-info-circle me-2"></i>
            请先在顶部导航栏选择一个项目，或者 <a href="/aitest/projects/" class="alert-link">创建新项目</a>
        </div>
    </div>
</div>

<!-- 项目概览 -->
<div class="row" id="projectOverview" style="display: none;">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-project-diagram me-2"></i>项目概览
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6 id="projectName">项目名称</h6>
                        <p class="text-muted" id="projectDescription">项目描述</p>
                        <small class="text-muted">
                            创建时间: <span id="projectCreatedAt">-</span>
                        </small>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="/aitest/projects" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-1"></i>管理项目
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row" id="statsCards" style="display: none;">
    <div class="col-md-3 mb-4">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="documentsCount">0</h4>
                        <p class="card-text">文档数量</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-alt fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="/aitest/documents" class="text-white text-decoration-none">
                    查看详情 <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="requirementsCount">0</h4>
                        <p class="card-text">需求功能点</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-list fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="/aitest/requirements" class="text-white text-decoration-none">
                    查看详情 <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="testCasesCount">0</h4>
                        <p class="card-text">测试用例</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-vial fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="/aitest/tests" class="text-white text-decoration-none">
                    查看详情 <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="knowledgeItemsCount">0</h4>
                        <p class="card-text">知识库条目</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-database fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="/aitest/knowledge" class="text-white text-decoration-none">
                    查看详情 <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row" id="quickActions" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="/aitest/documents" class="btn btn-outline-primary w-100">
                            <i class="fas fa-upload me-2"></i>上传文档
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/aitest/requirements" class="btn btn-outline-success w-100">
                            <i class="fas fa-magic me-2"></i>提取需求
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/aitest/tests" class="btn btn-outline-info w-100">
                            <i class="fas fa-plus me-2"></i>生成测试用例
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/aitest/knowledge" class="btn btn-outline-warning w-100">
                            <i class="fas fa-search me-2"></i>搜索知识库
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动 -->
<div class="row mt-4" id="recentActivity" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>最近活动
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush" id="activityList">
                    <!-- 活动列表将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    checkCurrentProject();
});

// 检查当前项目
function checkCurrentProject() {
    $.get('/aitest/api/current_project', function(response) {
        if (response.project_id) {
            loadProjectData(response.project_id);
        } else {
            showProjectPrompt();
        }
    });
}

// 显示项目选择提示
function showProjectPrompt() {
    $('#projectPrompt').show();
    $('#projectOverview, #statsCards, #quickActions, #recentActivity').hide();
}

// 加载项目数据
function loadProjectData(projectId) {
    $('#projectPrompt').hide();
    $('#projectOverview, #statsCards, #quickActions, #recentActivity').show();
    
    // 加载项目详情
    $.get(`/aitest/projects/api/${projectId}`, function(response) {
        if (response.success) {
            const project = response.data;
            $('#projectName').text(project.name);
            $('#projectDescription').text(project.description || '暂无描述');
            $('#projectCreatedAt').text(formatDate(project.created_at));
            
            // 更新统计数据
            const stats = project.stats || {};
            $('#documentsCount').text(stats.documents || 0);

            // 需求功能点统计：总数（AI生成数量/修改数量）
            const reqTotal = stats.requirements || 0;
            const reqAiGenerated = stats.requirements_ai_generated || 0;
            const reqAiModified = stats.requirements_ai_modified || 0;
            $('#requirementsCount').html(`${reqTotal}${reqAiGenerated > 0 ? `<br><small>(${reqAiGenerated}/${reqAiModified})</small>` : ''}`);

            // 用户测试用例统计：总数（AI生成数量/修改数量）
            const testTotal = stats.test_cases || 0;
            const testAiGenerated = stats.test_cases_ai_generated || 0;
            const testAiModified = stats.test_cases_ai_modified || 0;
            const autoTestTotal = stats.automation_test_cases || 0;
            const autoTestAiGenerated = stats.automation_test_cases_ai_generated || 0;
            const autoTestAiModified = stats.automation_test_cases_ai_modified || 0;
            const totalTests = testTotal + autoTestTotal;
            const totalAiGenerated = testAiGenerated + autoTestAiGenerated;
            const totalAiModified = testAiModified + autoTestAiModified;
            $('#testCasesCount').html(`${totalTests}${totalAiGenerated > 0 ? `<br><small>(${totalAiGenerated}/${totalAiModified})</small>` : ''}`);

            $('#knowledgeItemsCount').text(stats.knowledge_items || 0);
        }
    });
    
    // 加载最近活动（这里可以根据需要实现）
    loadRecentActivity(projectId);
}

// 加载最近活动
function loadRecentActivity(projectId) {
    // 这里可以实现加载最近活动的逻辑
    const activityList = $('#activityList');
    activityList.html(`
        <div class="list-group-item">
            <div class="d-flex w-100 justify-content-between">
                <h6 class="mb-1">欢迎使用AI智能测试平台</h6>
                <small>刚刚</small>
            </div>
            <p class="mb-1">您可以开始上传文档、提取需求、生成测试用例等操作。</p>
        </div>
    `);
}

// 刷新页面数据
function refreshPageData() {
    checkCurrentProject();
}
</script>
{% endblock %}
