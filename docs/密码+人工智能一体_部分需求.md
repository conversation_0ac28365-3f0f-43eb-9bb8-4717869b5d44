## 6.1. 产品运行环境

软件环境：  

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-E-0001</td><td>操作系统</td><td>Ubuntu 22.04.4 LTS</td><td>高</td></tr><tr><td>PR-E-0002</td><td>JAVA库</td><td>OpenJDK 17</td><td>高</td></tr><tr><td>PR-E-0003</td><td>Nginx</td><td>sansec/3.20.2</td><td>高</td></tr><tr><td></td><td></td><td>使用公用组件JCE-5.3.3.22版本； 监控组件使用2.1版本？？</td><td>高</td></tr><tr><td></td><td>Python</td><td>python3.10+</td><td></td></tr><tr><td></td><td>Go</td><td></td><td></td></tr></table></body></html>

硬件配置：

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-E-0004</td><td>AI一体机整 体配置</td><td>CPU: Intel Xeon Gold 6430 32核64高 线程*2 内存：32GB DDR4 RECC*8 硬盘1：480GSATA*2 硬盘2：3.84TNVME*2 GPU: GeForce RTX 4090 24GB *4 电源：2000W*2 Ukey : XT200*3</td><td></td></tr></table></body></html>

## 6.2. 功能清单

1. 监控组件的适配（以及开发，考虑是否扩展指标）

2. secuLlama 服务的启停--支持参数传入

3. 模型的展示  
4. 监控的管理  
5. 安全认证  
6. 数据脱敏  
7. 数据围栏  
8.智能应用门户

## 6.3. 功能说明

![](images/c63310e3dab530d594d362584a9409fb23b862ce9c46b3071217412b0515a8c4.jpg)
```text
这张图片展示了一个系统架构图，描述了不同角色（运维人员、系统管理员、门户管理员和普通用户）在系统中的操作流程和交互方式。以下是详细描述：

### 角色与操作
1. **运维人员**
   - **1-1 登录**：运维人员登录系统。
   - **1-2 启动**：启动运行框架。
   - **1-3 后台执行**：负责管理运行框架的启动、监控硬件的使用情况以及后续添加模型的管理。

2. **系统管理员**
   - **2-1 登录**：系统管理员登录系统。
   - **2-2 创建门户管理员**：创建门户管理员。
   - **2-3 授权、请求转发**：进行授权和请求转发。
   - **2-4 启动**：启动相关组件。
   - **2-5 添加供应商**：添加供应商。

3. **门户管理员**
   - **3-1 登录**：门户管理员登录系统。
   - **3-2 创建普通用户**：创建普通用户。
   - **3-3 登录dify实例，在dify实例内操作**：登录dify实例并进行操作。

4. **普通用户**
   - **4-1 登录**：普通用户登录系统。
   - **4-2 统一门户页面**：访问统一门户页面。
   - **4-3 api调用**：通过API调用进行操作。
   - **4-4 访问具体实例的应用**：访问具体实例的应用。

### 系统组件与流程
1. **nginx**
   - 作为前端服务器，处理用户的登录请求和业务调用。

2. **一体机管理系统进程**
   - 负责启动运行框架、监控硬件情况。

3. **统一web平台**
   - 进行鉴权、转发、管理门户管理员的操作。

4. **智能应用管理&统一门户**
   - 管理智能应用和统一门户。

5. **Dify实例1**
   - 具体的应用实例，可以进行启动、接口调用等操作。

6. **secuLlama/VLLM管理组件**
   - 容器化部署的管理组件。

7. **Deepseek系列模型**
   - 包括Qwen系列模型和Llama系列模型，用于大模型运算。

### 流程说明
- 运维人员启动运行框架后，系统管理员和门户管理员分别进行相应的管理和操作。
- 普通用户通过统一门户页面访问具体的应用实例，并通过API进行调用。
- 整个系统通过nginx进行前端处理，通过统一web平台进行鉴权和转发，最终通过Dify实例和Deepseek系列模型进行具体的应用操作和大模型运算。

这张图清晰地展示了各个角色在系统中的职责和操作流程，以及系统各组件之间的交互关系。
```


## 6.4. 监控组件

6.4.1. 监控组件适配

### PR-F-1001 监控组件安装适配

<html><body><table><tr><td>需求编号</td><td>PR-F-1001</td><td>需求名称</td><td>监控组件安装适配</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">1、需要在AI一体机上安装监控组件以及所需的基础运行环境； 2、保证监控组件在AI一体机上稳定运行； 3、提供可监控指标的汇总表。</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>輸入輸出約束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">监控组件安装成功，并可稳定运行</td></tr></table></body></html>

<html><body><table><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

### PR-F-1002 监控组件功能测试

<html><body><table><tr><td>需求编号</td><td>PR-F-1002</td><td>需求名称</td><td>监控组件功能测试</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">1.测试监控组件是否可以正确获取各项指标； 2.整理指标集，确定哪些指标需要在管理系统上展示，对外输出指标汇总表；</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>输入输出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">1、输出指标汇总表； 2、监控组件各指标采集正常；</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

### 6.4.2. 监控组件功能扩展

#### PR-F-1101 AI 一体机特殊指标整理

<html><body><table><tr><td>需求编号</td><td>PR-F-1101</td><td></td><td>需求名称AI一体机特殊指标整理</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">收集需要单独采集的指标-显卡的数量、显存、温度、进程的使用情况，还需要考虑 国产GPU的兼容问题，将指标汇总至指标汇总表；</td></tr><tr><td>业务流程</td><td colspan="5">gpu列表，每个gpu对象包含：温度、显存、已用显存、 不涉及</td></tr><tr><td>輪入輸出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">输出指标汇总表</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

#### PR-F-1102 AI 一体机特殊指标获取

<html><body><table><tr><td>需求编号</td><td>PR-F-1102</td><td>需求名称</td><td>AI一体机特殊指标获取</td><td>优先级</td><td>高</td></tr></table></body></html>

<html><body><table><tr><td>需求描述</td><td>根据整理的需要单独处理的指标，研究、评估采集方式，并考虑是否可以集成到监 控组件，如果需要集成到监控组件，需要支持通过监控组件获取指标。</td></tr><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td>如果可以集成至监控组件，可以通过监控组件获取对应的指标。</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>
