# 密码+人工智能一体机管理系统项目需求规格说明书

V1.0

三未信安科技股份有限公司2025 年 04 月 03 日

版本修订说明

<html><body><table><tr><td>版本</td><td>修改内容</td><td>修改人</td><td>修改日期</td><td>审核人</td><td>发布日期</td></tr><tr><td>V1.0</td><td>创建</td><td>万军</td><td>2025.04.03</td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

# 目录

目录.. 3  
1. 简介 .  
1.1. 术语和缩写 ...  
1.2. 参考资料 .. . 8  
2. 产品描述 . .......8  
3. 用户业务场景分析 ....  
4. 与现有产品差异 ..  
5. 约束与限制 .. .10  
6. 需求详细描述 . .10  
6.1. 产品运行环境 10  
6.2. 功能清单 .. 11  
6.3. 功能说明 .. 11  
6.4. 监控组件 ... 12  
6.4.1.监控组件适配 . 12  
PR-F-1001 监控组件安装适配 .. 12  
PR-F-1002 监控组件功能测试 .. 13  
6.4.2.监控组件功能扩展 . 13  
PR-F-1101 AI 一体机特殊指标整理 . . 13  
PR-F-1102 AI 一体机特殊指标获取 . 13  
6.5. 管理系统相关需求 14  
6.5.1. secuLlama 服务管理... 14  
6.2.1.1 secuLlama 服务启动 ... 14  
secuLlama 服务启动. 14  
6.2.1.2 secuLlama 服务停止 ... 14  
secuLlama 服务停止.. 14  
6.2.1.3 secuLlama 服务状态查看 ..... 15  
secuLlama 服务状态查看 .... 15  
6.2.1.4 secuLlama 服务版本查看 ..... 15  
secuLlama 服务版本查看 .. ..... 15  
6.5.2.模型管理 .. . 16  
6.5.2.1. 查看正在运行的模型 .... 16  
查看正在运行的模型 .. ..... 16  
6.5.3.监控管理 . 17  
6.5.3.1. 监控面板开发.. 17  
监控面板开发 . 17  
6.5.3.2. AI 一体机特殊指标梳理... 17  
AI 一体机特殊指标梳理...... 18  
6.5.3.3. 告警管理--本迭代由于不考虑数据库问题，暂不开发 . 18

告警管理 . 18

通过内嵌监控组件的告警管理列表，实现告警功能，包含告警规则、告警联系人、告警列表、邮件服务器配  
置、告警历史等。 18  
6.5.4.安全认证 .... . 18  
6.5.4.1. 基于 Ukey 的认证... 18  
基于 Ukey 的认证 18  
6.5.4.2. 基于配置文件的认证 .. .... 19  
基于配置文件的认证 ... 19  
6. 智能应用统一门户相关需求 . 19  
6.6.1.基础网关相关需求 .... 19  
6.6.1.1. 用户身份和用户类型 0  
用户身份和用户类型 . 20  
6.6.1.2. 门户管理服务 API 代理和鉴权. . 20  
门户管理服务 API 代理和鉴权 .. 20  
6.6.1.3. 门户访问代理和鉴权 .. 21  
门户访问代理和鉴权 . 21  
6.6.1.4. 应用访问代理和鉴权  
应用访问代理和鉴权 .. 21  
6.6.2.门户管理员管理 . 22  
6.6.2.1. 新增门户管理员 22  
新增门户管理员 . 22  
6.6.2.2. 编辑门户管理员. 22  
编辑门户管理员 . ... 22  
6.6.2.3. 删除门户管理员. 23  
删除门户管理员 . 23  
6.6.2.4. 门户管理员列表. 23  
门户管理员列表 .. 23  
6.6.2.5. 门户管理员登录. .24  
门户管理员登录 . 24  
6.6.2.6. 门户管理员退出.. 24  
门户管理员退出 . .24  
6.6.3.门户用户管理 .. 25  
6.6.3.1. 新增用户. 25  
新增用户. 25  
6.6.3.2. 编辑用户.. 26  
编辑用户. . 26  
6.6.3.3. 删除用户.. 26  
删除用户 . 26  
6.6.3.4. 门户用户列表.. .... 26  
门户用户列表 . .. 26  
6.6.3.5. 门户用户登录.. 27  
门户用户登录 .... 27  
6.6.3.6. 门户用户退出... .. 28  
门户用户退出 . 28

6.6.3.7. 启用门户用户. 28启用门户用户 .. .. 286.6.3.8. 禁用门户用户. 29禁用门户用户 .. 296.6.4.门户管理（Dify实例管理） 296.6.4.1. 创建门户. 29创建门户 . 296.6.4.2. 编辑门户信息.. 30编辑门户信息 ..... 306.6.4.3. 门户列表... 30门户列表 .. 306.6.4.4. 启动门户.. 31门户列表... . 316.6.4.5. 停止门户..... . 31停止门户... 316.6.4.6. 删除门户.. 32删除门户 . 326.6.4.7. 跳转管理端.. 32跳转管理端..... . 326.6.4.8. 跳转门户. 33跳转管理端..... 336.6.4.9. 首页配置................. ...... 33首页配置 . 336.6.4.10. 访问控制配置.. 34访问控制配置 . 346.6.5. Dify容器化多实例部署改造.. ...... 346.6.5.1. Dify 多实例部署&网络隔离&目录隔离 34Dify 多实例部署&网络隔离&目录隔离 .. 346.6.5.2. Dify 实例连接外部数据库.... . 错误!未定义书签。Dify 实例连接外部数据库.. . 错误!未定义书签。6.6.5.3. Dify 实例连接外部 Redis.... . 错误!未定义书签。Dify 实例连接外部 Redis.. . 错误!未定义书签。6.6.5.4. 初始化供应商和模型 ... 35初始化供应商和模型 . . 356.6.5.5. Dify 实例自动化部署.... . 35Dify 实例自动化部署 . 356.6.6.应用市场 .. 错误!未定义书签。6.6.6.1. 应用模板管理.... . 错误!未定义书签。应用模板管理...... . 错误!未定义书签。6.6.6.2. 应用模板列表... . 错误!未定义书签。应用模板列表 .. . 错误!未定义书签。6.6.6.3. 应用安装.. . 错误!未定义书签。应用安装... . 错误!未定义书签。6.6.7.统一门 35

6.6.7.1. 访问门户首页... . 35  
访问门户首页 .. ... 35  
6.6.7.2. 用户信息展示. . 36  
门户用户信息 . ... 36  
6.6.7.3. 门户首页对话. . 37  
门户首页对话 .. .. 37  
6.6.7.4. 应用探索（门户应用列表） ... 37  
门户应用列表 . ...... 37  
6.6.7.5. 应用对话页... ... 38  
应用对话页 . .. 38  
6.6.7.6. 用户收藏应用、取消收藏应用 . . 错误!未定义书签。  
用户收藏应用、取消收藏 . . 错误!未定义书签。  
6.7. 接口需求 46  
6.8. 界面需求 ... . 46  
6.9. 性能需求 ... .. 46  
6.10. 可靠性/可用性需求 ... 46  
6.11. 安全性需求 .. 47  
6.1.1.数据脱敏 .. . 44  
6.1.2.数据围栏组件适配 .. 45  
6.12. 可维护性需求 .. 48  
6.13. 工作状态需求 ... 48  
6.14. 结构需求 .. . 49  
6.15. 环保需求 .. .. 49  
6.16. 认证需求 ... .... 50  
6.17. 用户文档需求 ... .. 50  
6.18. 客户特殊需求 .. . 51  
6.19. 法律法规要求 . . 51  
6.20. 国家及行业标准要求 .. . 51  
6.21. 失效模式分析(参见《设计失效模式和影响分析(DFMEA)库》) .. 51  
6.22. 其他需求 . . 51

# 1. 简介

## 1.1. 术语和缩写

<html><body><table><tr><td>编号 名词</td><td>说明</td><td></td></tr><tr><td>1</td><td>CryptoAI OneSystem</td><td>密码+人工智能一体机管理系统</td></tr><tr><td>2</td><td>secuLlama vLLM</td><td>基于Ollama添加安全访问策略之后的大模型运行框 架。 Python 库，大模型推理引擎。</td></tr><tr><td>3 3</td><td>secuLlm</td><td>自研大模型运行框架，实现多vLLM进程服务管理及 API请求转发，并添加安全访问策略。</td></tr><tr><td>5</td><td>Dify</td><td>Dify是一款开源的大语言模型(LLM)应用开发平台。 它融合了后端即服务（Backend as Service)和LLMOps 的理念，使开发者可以快速搭建生产级的生成式AI 应用</td></tr><tr><td>6</td><td>知识库</td><td>知识库（Knowledge）是一系列文档（Documents）的 集合，一个文档内可能包含多组内容分段（Chunks）， 知识库可以被整体集成至一个应用中作为检索上下文 使用，用户可以将企业内部文档、FAQ、规范信息等 内容上传至知识库，知识库会自动进行结构化处理。 当LLM接收到用户的问题后，将首先基于关键词在 知识库内检索内容。知识库将根据关键词，召回相关 度排名较高的内容区块，向LLM提供关键上下文以 辅助其生成更加精准的回答</td></tr><tr><td>6</td><td>应用</td><td>应用是指基于GPT等大语言模型构建的实际场景应 用。通过创建应用，用户可以将智能AI技术应用于 特定的需求，应用类型包括：聊天助手、文档生成应 用、Agent、对话流、工作流。</td></tr><tr><td>7</td><td>聊天助手应用</td><td>基于LLM 构建对话式交互的助手，通过交互式界面， 采用一问一答模式与用户持续对话，可以用在客户服 务、在线教育、医疗保健、金融服务等领域。这些应 用可以帮助组织提高工作效率、减少人工成本和提供 更好的用户体验。</td></tr><tr><td>8</td><td>文本生成应用</td><td>面向文本生成类任务的助手，例如撰写故事、文本分 类、翻译等 能够分解任务、推理思考、调用工具的对话式智能助</td></tr><tr><td>9</td><td>Agent应用</td><td>手 适用于定义等复杂流程的多轮对话场景，具有记忆功</td></tr><tr><td>10</td><td>对话流应用</td><td>能的应用编排方式</td></tr><tr><td>11</td><td>工作流应用</td><td>适用于自动化、批处理等单轮生成类任务的场景的应 用编排方式</td></tr></table></body></html>

## 1.2. 参考资料

包括引用的项目内资料、客户方资料等等。

# 2. 产品描述

三未信安基于密码安全与AI 技术的深厚积累，针对大模型本地化部署中的安全挑战，倾力打造"密码+"人工智能一体机产品。

产品深度融合国产硬件、大模型RAG 框架及全栈式安全防护能力，围绕“硬件+软件+服务”提供身份认证、数据隔离、内容过滤、模型保护等核心功能模块。面向各行业智能化升级需求，形成集智能问答交互、文档分析处理、数据治理优化等多功能于一体的“开箱即用”解决方案。通过对算力资源、算法模型与安全机制的统筹设计，全面兼顾业务效率与合规要求，真正实现模型部署易、管理省、安全强，让企业在智能化转型中稳步前行。

三未信安AI 一体机管理系统用于管理、监控 AI 一体机，利用该系统，可以将后台执行的功能通过页面来提供，方便客户使用、管理、监控一体机。该管理系统的整体架构图如下，核心为应用层：

![](images/d93db1d0ae93ff31f65b35a5e4a52b2eebfa5f874970d977deca86a02f8ea437.jpg)
```text
这张图片展示了一个系统的架构图，从用户接入到硬件层的各个组成部分。系统分为五个主要层次：应用层、模型层、系统层、硬件层和监控组件。

1. **用户接入**：
   - 用户可以通过浏览器、移动应用、社交应用、API Client 和第三方应用等方式接入系统。

2. **应用层**：
   - 包含多个Dify实例（如Dify实例1和Dify实例2），每个实例包含智能助手、工作流和知识库等功能。
   - 还有一个数据围栏模块。
   - 智能应用统一门户提供了基础网关、Dify实例管理、统一门户、应用市场、用户权限管理和围栏管理等功能。

3. **模型层**：
   - 包含SecuLlama和VLLM管理组件两个部分。
   - SecuLlama包括LLM API网关、模型安全、输出安全和模型推理等功能，并支持DeepSeek、Qwen系列和Llama系列等模型。
   - VLLM管理组件包括VLLM管理、模型安全、输出安全和LLM API网关等功能，并支持VLLM1、VLLM2和VLLM3等模型。

4. **系统层**：
   - 包含驱动、CUDA/CANN、数据库、向量数据库和知识库文件透明加密等功能。
   - 运行在操作系统OS上。

5. **硬件层**：
   - 包含GPU/NPU、NIC、DISK、CPU和密码卡等硬件设备。

6. **监控组件**：
   - 提供服务管理、模型管理、监控面板和显卡面板等功能。

7. **一体机管理系统**：
   - 提供服务管理、模型管理、监控面板和显卡面板等功能。

整个系统通过这些层次和组件协同工作，为用户提供智能化的应用和服务。
```


说明：管理系统不涉及利用大模型进行推理、会话等业务，仅用来提供运维功能。具体的业务管理、运营功能敬请期待后续产品。

# 3. 用户业务场景分析

1. 运维场景：通过简单的操作即可将大模型轻松运行起来，提供服务，大大降低运维人员的运维压力，降低AI 使用的难度；2. 监控场景：通过管理系统可以实现对一体机的全面、实时监控，可以快速、准确的检测到设备的实时状态，在问题出现时，能够快速定位问题原因；3. 性能优化场景：由于降低了运维压力，且提供了实时的监控，运维人员可以投入更多的精力到大模型性能提升上，通过直观的监控，可能更加清晰的找到性能瓶颈点；4. 数据脱敏场景：提供一系列针对敏感数据的识别和处置方案，应用多种隐私合规标准，对原始数据进行分级打标、判断敏感级别和实施相应的脱敏处理；5. 数据围栏场景：提供输入输出内容风险检测的能力，帮助用户发现色情、暴力、惊悚、敏感、禁限、辱骂等风险内容或元素，降低人工审核成本，提升内容质量；6. 数据隔离场景：通过多Dify 实例的管理功能+租户管理，为每个租户构建独立知识库，确保数据互不干扰，实现知识库隔离。同时，依据不同租户需求，通过精细化的AI 应用权限管理，保障各租户在使用AI 应用时的数据安全与权限边界明确。

# 4. 与现有产品差异

<html><body><table><tr><td>现有产品功能点</td><td>新需求差异描述</td><td>备注</td></tr><tr><td>当前AI一体机没有管理页面，只 能通过后台命令进行管理；且不具 备监控功能。</td><td>增加管理页面，支持通过页面进行简单 的运维操作；支持监控管理，实现对一 体机状态的实时监控。</td><td></td></tr><tr><td>当前AI一体机不具有敏感数据脱 敏处理的能力，存在敏感数据泄漏 风险。</td><td>支持多种数据格式，支持多种敏感数据 识别规则，支持多种脱敏算法。</td><td></td></tr><tr><td>当前AI大模型仅支持简单的关键 词屏蔽或黑白名单规则，无法处理 复杂语义或变体风险内容。</td><td>针对不良价值观、涉黄、违法犯罪等安 全问题，降低大模型拒答率，支持风险 问题的正向引导和纠偏。</td><td></td></tr></table></body></html>

# 5. 约束与限制

说明在实现时所必须满足的条件和所受的限制，以及相应的原因。如：必须使用或者避免的特定技术、工具、编程语言和数据库、企业策略、政府法规或工业标准。

# 6. 需求详细描述

本项目将分多次迭代实现，第一次迭代重点实现基础的运维与监控，dify 服务管理，智能应用门户，并集成数据脱敏组件与内容安全检测组件：

1. 监控组件的适配（以及开发，考虑是否扩展指标）

2. secuLlama/secuLlm 服务的启停--支持参数传入

3. 模型的展示  
4. 监控的管理  
5. 安全认证  
6. 数据脱敏  
7. 数据围栏  
8.智能应用门户

## 6.1. 产品运行环境

软件环境：  

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-E-0001</td><td>操作系统</td><td>Ubuntu 22.04.4 LTS</td><td>高</td></tr><tr><td>PR-E-0002</td><td>JAVA库</td><td>OpenJDK 17</td><td>高</td></tr><tr><td>PR-E-0003</td><td>Nginx</td><td>sansec/3.20.2</td><td>高</td></tr><tr><td></td><td></td><td>使用公用组件JCE-5.3.3.22版本； 监控组件使用2.1版本？？</td><td>高</td></tr><tr><td></td><td>Python</td><td>python3.10+</td><td></td></tr><tr><td></td><td>Go</td><td></td><td></td></tr></table></body></html>

硬件配置：

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-E-0004</td><td>AI一体机整 体配置</td><td>CPU: Intel Xeon Gold 6430 32核64高 线程*2 内存：32GB DDR4 RECC*8 硬盘1：480GSATA*2 硬盘2：3.84TNVME*2 GPU: GeForce RTX 4090 24GB *4 电源：2000W*2 Ukey : XT200*3</td><td></td></tr></table></body></html>

## 6.2. 功能清单

1. 监控组件的适配（以及开发，考虑是否扩展指标）

2. secuLlama 服务的启停--支持参数传入

3. 模型的展示  
4. 监控的管理  
5. 安全认证  
6. 数据脱敏  
7. 数据围栏  
8.智能应用门户

## 6.3. 功能说明

![](images/c63310e3dab530d594d362584a9409fb23b862ce9c46b3071217412b0515a8c4.jpg)
```text
这张图片展示了一个系统架构图，描述了不同角色（运维人员、系统管理员、门户管理员和普通用户）在系统中的操作流程和交互方式。以下是详细描述：

### 角色与操作
1. **运维人员**
   - **1-1 登录**：运维人员登录系统。
   - **1-2 启动**：启动运行框架。
   - **1-3 后台执行**：负责管理运行框架的启动、监控硬件的使用情况以及后续添加模型的管理。

2. **系统管理员**
   - **2-1 登录**：系统管理员登录系统。
   - **2-2 创建门户管理员**：创建门户管理员。
   - **2-3 授权、请求转发**：进行授权和请求转发。
   - **2-4 启动**：启动相关组件。
   - **2-5 添加供应商**：添加供应商。

3. **门户管理员**
   - **3-1 登录**：门户管理员登录系统。
   - **3-2 创建普通用户**：创建普通用户。
   - **3-3 登录dify实例，在dify实例内操作**：登录dify实例并进行操作。

4. **普通用户**
   - **4-1 登录**：普通用户登录系统。
   - **4-2 统一门户页面**：访问统一门户页面。
   - **4-3 api调用**：通过API调用进行操作。
   - **4-4 访问具体实例的应用**：访问具体实例的应用。

### 系统组件与流程
1. **nginx**
   - 作为前端服务器，处理用户的登录请求和业务调用。

2. **一体机管理系统进程**
   - 负责启动运行框架、监控硬件情况。

3. **统一web平台**
   - 进行鉴权、转发、管理门户管理员的操作。

4. **智能应用管理&统一门户**
   - 管理智能应用和统一门户。

5. **Dify实例1**
   - 具体的应用实例，可以进行启动、接口调用等操作。

6. **secuLlama/VLLM管理组件**
   - 容器化部署的管理组件。

7. **Deepseek系列模型**
   - 包括Qwen系列模型和Llama系列模型，用于大模型运算。

### 流程说明
- 运维人员启动运行框架后，系统管理员和门户管理员分别进行相应的管理和操作。
- 普通用户通过统一门户页面访问具体的应用实例，并通过API进行调用。
- 整个系统通过nginx进行前端处理，通过统一web平台进行鉴权和转发，最终通过Dify实例和Deepseek系列模型进行具体的应用操作和大模型运算。

这张图清晰地展示了各个角色在系统中的职责和操作流程，以及系统各组件之间的交互关系。
```


## 6.4. 监控组件

6.4.1. 监控组件适配

### PR-F-1001 监控组件安装适配

<html><body><table><tr><td>需求编号</td><td>PR-F-1001</td><td>需求名称</td><td>监控组件安装适配</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">1、需要在AI一体机上安装监控组件以及所需的基础运行环境； 2、保证监控组件在AI一体机上稳定运行； 3、提供可监控指标的汇总表。</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>輸入輸出約束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">监控组件安装成功，并可稳定运行</td></tr></table></body></html>

<html><body><table><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

### PR-F-1002 监控组件功能测试

<html><body><table><tr><td>需求编号</td><td>PR-F-1002</td><td>需求名称</td><td>监控组件功能测试</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">1.测试监控组件是否可以正确获取各项指标； 2.整理指标集，确定哪些指标需要在管理系统上展示，对外输出指标汇总表；</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>输入输出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">1、输出指标汇总表； 2、监控组件各指标采集正常；</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

### 6.4.2. 监控组件功能扩展

#### PR-F-1101 AI 一体机特殊指标整理

<html><body><table><tr><td>需求编号</td><td>PR-F-1101</td><td></td><td>需求名称AI一体机特殊指标整理</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">收集需要单独采集的指标-显卡的数量、显存、温度、进程的使用情况，还需要考虑 国产GPU的兼容问题，将指标汇总至指标汇总表；</td></tr><tr><td>业务流程</td><td colspan="5">gpu列表，每个gpu对象包含：温度、显存、已用显存、 不涉及</td></tr><tr><td>輪入輸出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">输出指标汇总表</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

#### PR-F-1102 AI 一体机特殊指标获取

<html><body><table><tr><td>需求编号</td><td>PR-F-1102</td><td>需求名称</td><td>AI一体机特殊指标获取</td><td>优先级</td><td>高</td></tr></table></body></html>

<html><body><table><tr><td>需求描述</td><td>根据整理的需要单独处理的指标，研究、评估采集方式，并考虑是否可以集成到监 控组件，如果需要集成到监控组件，需要支持通过监控组件获取指标。</td></tr><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td>如果可以集成至监控组件，可以通过监控组件获取对应的指标。</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

## 6.5. 管理系统相关需求

6.5.1. secuLlama 服务/secullm 服务管理

6.2.1.1 secuLlama/secullm 服务服务启动  

<html><body><table><tr><td>需求编号</td><td>PR-F-2001</td><td>需求名称secuLlama服务启动</td><td></td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td>2、如果服务已经启动，则禁止重复操作；</td><td></td><td>1、收集secuLlama启动时支持配置哪些参数，支持通过页面可以实现对 secuLlama 的服务启动，并支持传参；如果传参为空，则使用默认的配置参数；从当前启动脚 本上来看，支持的参数：监听端口、SSL 配置(tls，gmtls，none)、证书的配置</td><td></td><td></td></tr><tr><td></td><td></td><td>3、支持后台修改配置：修改配置后需要删除原来容器，重启启动容器。</td><td></td><td></td><td></td></tr><tr><td>业务流程</td><td>不涉及 不涉及</td><td></td><td></td><td></td><td></td></tr><tr><td>输入輸出约束 验收标准</td><td></td><td></td><td>可以通过页面完成secuLlama服务的启动，并通过后台可以看到该进程存在；此外，</td><td></td><td></td></tr><tr><td>其它说明</td><td>不涉及</td><td>支持启动时传递配置参数。该接口无需压测。</td><td></td><td></td><td></td></tr></table></body></html>

#### 6.2.1.2 secuLlama 服务/secullm 服务停止

<html><body><table><tr><td>需求编号</td><td>PR-F-2002</td><td>需求名称secuLlama服务停止</td><td></td><td>优先级</td><td>高</td></tr><tr><td rowspan="2">需求描述</td><td colspan="3">1、支持通过页面可以实现对secuLlama/secullm服务的服务停止；</td><td></td><td></td></tr><tr><td colspan="3">2、如果服务已经停止，则禁止重复操作； 3、停止服务时，需要考虑是否有模型正在运行；弹窗需要提示：停止服务，会停</td><td></td><td></td></tr></table></body></html>

<html><body><table><tr><td></td><td></td></tr><tr><td>业务流程</td><td></td></tr><tr><td>输入输出约束 不涉及</td><td></td></tr><tr><td>验收标准</td><td>可以通过页面完成 secuLlama 服务的停止，并通过后台可以看到该进程已停止，该 接口无需压测。 不涉及</td></tr><tr><td>其它说明</td><td></td></tr></table></body></html>

#### 6.2.1.3 secuLlama 服务/secullm 服务状态查看

6.2.1.4 secuLlama 服务/secullm 服务版本查看  

<html><body><table><tr><td>需求编号</td><td>PR-F-2003</td><td></td><td>需求名称secuLlama服务状态查看</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="3">1、可以通过页面看到 secuLlama服务/secullm服务的状态，且与后台查询的状态一 致； 2、通过后台停止服务后，可以在页面更新状态，由于不需要数据库存储，每次页</td><td></td><td></td></tr><tr><td></td><td colspan="3">面显示都是查询的最新状态，因此不存在延迟性。</td><td></td><td></td></tr><tr><td>业务流程 輸入輸出约束</td><td colspan="3">不涉及 不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan="3">可以通过页面查看secuLlama服务的状态。该接口可压测。</td><td colspan="2"></td></tr><tr><td>其它说明</td><td colspan="3">不涉及</td><td colspan="2"></td></tr></table></body></html>

<html><body><table><tr><td>需求编号</td><td>PR-F-2004</td><td>需求名称</td><td>secuLlama服务版本查看</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td></td><td>1、可以通过页面看到secuLlama服务/secullm服务的版本信息。</td><td></td><td></td><td></td></tr><tr><td>业务流程</td><td>不涉及</td><td></td><td></td><td></td><td></td></tr><tr><td>输入输出约束</td><td>不涉及</td><td></td><td></td><td></td><td></td></tr></table></body></html>

<html><body><table><tr><td>验收标准</td><td>可以通过页面查看secuLlama服务的版本信息。该接口可压测。</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

#### 6.2.1.5 secuLlama 服务/secullm 服务接口手册下载

<html><body><table><tr><td>需求编号</td><td>PR-F-2005</td><td>需求名称</td><td>下载接口手册</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">1、可以通过页面下载接口手册。</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>输入输出約束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">可以通过页面下载接口手册。</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

### 6.5.2. 模型管理

6.5.2.1. 查看正在运行的模型

<html><body><table><tr><td>需求编号</td><td>PR-F-2101</td><td>需求名称</td><td>查看正在运行的模型</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="3">可以通过页面查看正在运行模型有哪些。 需要提供后台新增模型的功能说明。</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan="3">1、当secuLlama服务出于运行状态时，可以看到运行的模型； 2、当secuLlama服务处于停止状态时，进入该菜单需要提示服务未运行。</td><td></td><td></td></tr><tr><td>綸入輸出約束</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan="3">根据secuLlama的服务不同状态，显示不同的内容。</td><td></td><td></td></tr><tr><td>其它说明</td><td colspan="3">不涉及</td><td></td><td></td></tr></table></body></html>

### 6.5.3. apikey 管理

6.5.3.1. 查看 apikey

<html><body><table><tr><td>需求编号</td><td>PR-F-2301</td><td>需求名称</td><td>查看apikey</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">可以通过页面查看apikey 列表，默认使用****表示，可以查看明文的 apikey。</td></tr><tr><td>业务流程</td><td colspan="5"></td></tr><tr><td>输入输出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">1、默认情况下，apikey以***表示 2、点击查看，可以看到明文的 apikey 3、提供复制 apikey的功能按钮</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

### 6.5.4. 监控管理

6.5.4.1. 监控面板开发   

<html><body><table><tr><td>需求编号</td><td>PR-F-2201</td><td>需求名称</td><td>监控面板开发</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">结合指标汇总表，开发监控面板，用于显示监控指标</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>输入输出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">能够通过监控面板查看指标情况。</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

#### 6.5.4.2. AI 一体机特殊指标梳理

<html><body><table><tr><td>需求编号</td><td>PR-F-2202</td><td></td><td>需求名称AI一体机特殊指标梳理</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="3">需要结合监控组件的特殊指标梳理需求，考虑是将指标采集的实现集成至监控组 件，还是监控管理模块；为了方便后续多台设备的监控管理，以及对接监管平台， 建议还是集成到监控组件，便于后续扩展。</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>输入輪出约束</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan="3">无</td><td></td><td></td></tr><tr><td>其它说明</td><td colspan="3">不涉及</td><td></td><td></td></tr></table></body></html>

6.5.4.3. 告警管理--本迭代由于不考虑数据库问题，暂不开发

<html><body><table><tr><td>需求编号</td><td>PR-F-2203</td><td>需求名称</td><td>告警管理</td><td>优先级</td><td>低</td></tr><tr><td>需求描述</td><td colspan="5">通过内嵌监控组件的告警管理列表，实现告警功能，包含告警规则、告 警联系人、告警列表、邮件服务器配置、告警历史等。</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>输入输出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">告警管理功能正常。</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

### 6.5.5. 安全认证

6.5.5.1. 基于 Ukey 的认证

<html><body><table><tr><td>需求编号</td><td>PR-F-2301</td><td>需求名称</td><td>基于Ukey的认证</td><td>优先级</td><td>高</td></tr></table></body></html>

6.5.5.2. 基于配置文件的认证  

<html><body><table><tr><td>需求描述</td><td>考虑不使用数据库，利用内置密码卡的密钥对Ukey下发证书，后续登录时，利用 密码卡中的密钥对证书进行验签即可。</td></tr><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td>使用正确的Ukey可以成功登录管理系统；使用错误的Ukey无法登录。</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

<html><body><table><tr><td>需求编号</td><td>PR-F-2302</td><td>需求名称</td><td>基于配置文件的认证</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">通过配置文件可以登录管理系统，具体展示形式、实现方式待定。</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>輸入輸出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">通过配置文件可以登录管理系统。</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

## 6.6. 智能应用统一门户相关需求

### 6.6.1. 基础网关相关需求

基础网关作为用户访问智能应用统一门户的前置代理，主要功能如下：

1. 用户身份认证：对所有访问统一门户的用户进行身份认证和权限控制2. 门户管理代理：门户管理员配置门户时，基础网关负责将请求转发到后台管理服务中，完成对门户的配置3. 门户访问代理：为每一个智能应用统一门户提供独立的访问地址

4. 应用访问代理：用户访问应用时，基础网关负责将用户请求转发到对应的 Dify实例中，完成用户、应用、模型的交互

#### 6.6.1.1. 用户身份和用户类型

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td></td><td>需求名称用户身份和用户类型</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">1.用户身份：即用户登录门户后系统为用户分配的 token，是鉴权时获取用户信息、 用户类型的凭证 2.用户类型分为四类： a.系统管理员：super用户，负责Dify实例管理、门户管理、应用模板管理 b.aduit用户：用于查看审计日志和业务日志 c.门户管理员：oper用户，一个门户至少有一个门户管理员，负责应用管理、门户 用户管理、Dify控制台管理 d.门户用户：普通用户，可以访问某个门户，使用门户下的应用</td></tr><tr><td>业务流程 輸入輸出約束</td><td colspan="3">不涉及 不涉及</td><td></td><td></td></tr><tr><td></td><td colspan="3">不同类型的用户可以访问对应的页面</td><td colspan="2"></td></tr><tr><td>验收标准 其它说明</td><td colspan="3">不涉及</td><td colspan="3"></td></tr></table></body></html>

#### 6.6.1.2. ⻔户管理服务 API 代理

<html><body><table><tr><td>需求编号</td><td>PR-F-6512</td><td></td><td>需求名称门户管理服务API代理</td><td>优先级</td><td>关闭</td></tr><tr><td>需求描述</td><td colspan="5">1.通过基础网关，实现门户管理服务API的访问代理：前端调用门户管理服务API 时，需要先经过基础网关，基础网关进行请求转发，转发至智能门户系统。 说明：鉴权交由统一web 平台</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>输入输出约束</td><td colspan="5">不涉及</td></tr></table></body></html>

<html><body><table><tr><td>验收标准</td><td>登录成功后，访问前端页面时，可以正常访问；未登录时，通过复制url无法访问。</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

#### 6.6.1.3. 门户访问代理和鉴权

<html><body><table><tr><td>需求编号</td><td>PR-F-6512</td><td>需求名称门户访问代理</td><td></td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">1.门户访问代理：当门户用户访问门户时，通过基础网关引导用户跳转到正确的门 户地址。 2.门户访问鉴权：基础网关不进行鉴权，仅转发。</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>綸入綸出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">1.门户用户能跳转到正确的门户页面</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

#### 6.6.1.4. 应用访问代理和鉴权

<html><body><table><tr><td>需求编号</td><td>PR-F-6512</td><td>需求名称</td><td>应用访问代理</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">1.应用访问代理：当门户用户访问应用时，通过基础网关引导用户跳转到正确的应 用地址 2.应用访问鉴权：基础网关不鉴权，仅转发，鉴权由统一web平台处理。</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>输入输出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">1.门户用户登录系统后，能跳转到正确的应用页面。 2.门户用户未登录系统，通过复制url的方式无法跳转至应用页面。</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

#### 6.6.1.5. 动态更新

<html><body><table><tr><td>需求编号</td><td>PR-F-6512</td><td>需求名称</td><td>动态更新路由</td><td>优先级</td><td>高</td></tr></table></body></html>

<html><body><table><tr><td>需求描述</td><td>当访问具体 dify实例应用的请求到达基础网关后，基础网关先判断当前的路由配置 信息是否包含该dify 实例，如果包含，则直接转发；如果不包含，则查询数据库， 如果数据库中有，则更新路由，继续转发；如果数据库中也没有，返回404</td></tr><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td>Dify实例存在时，门户用户访问具体应用，访问成功； Dify实例不存在时，门户用户访问报错。</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

### 6.6.2. 门户管理员管理

#### 6.6.2.1. 新增⻔户管理员

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>新增门户管理员</td><td>优先级</td><td>关闭</td></tr><tr><td>需求描述</td><td colspan="5">权限归属：系统管理员 1.系统管理员可新增门户管理员账号，新增时必须绑定所属门户 2.一个门户可以绑定多个门户管理员</td></tr><tr><td>业务流程</td><td colspan="5">3.门户管理员关联到Dify 实例中的管理员账号，具有Dify实例访问权限 不涉及</td></tr><tr><td>输入输出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">用户新增成功</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

#### 6.6.2.2. 编辑⻔户管理员

<html><body><table><tr><td>需求編号</td><td>PR-F-6511</td><td>需求名称</td><td>编辑门户管理员</td><td>优先级</td><td>关闭</td></tr><tr><td rowspan="2">需求描述</td><td colspan="3">权限归属：系统管理员</td><td></td><td></td></tr><tr><td colspan="3">1.系统管理员可编辑门户管理员基本信息，不可更改所属门户</td><td></td><td></td></tr></table></body></html>

<html><body><table><tr><td>业务流程</td><td></td></tr><tr><td>輸入輸出約束 不涉及</td><td></td></tr><tr><td>验收标准</td><td>用户编辑成功</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

#### 6.6.2.3. 删除⻔户管理员

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>删除门户管理员</td><td>优先级</td><td>关闭</td></tr><tr><td>需求描述</td><td colspan="5">权限归属：系统管理员 1.系统管理员可删除门户管理员</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>输入输出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">用户删除成功</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

#### 6.6.2.4. ⻔户管理员列表

<html><body><table><tr><td>需求編号</td><td>PR-F-6511</td><td>需求名称</td><td>门户管理员列表</td><td>优先级</td><td>关闭</td></tr><tr><td>需求描述</td><td colspan="5">权限归属：系统管理员 1.列表展示： －分页展示所有门户的管理员信息，默认按创建时间倒序排列。 －显示字段：用户名、用户类型、所属门户、最后登录时间。 2.筛选与搜索: －支持按用户类型、所属门户组合筛选。 -支持模糊搜索用户名 3.操作列：</td></tr></table></body></html>

<html><body><table><tr><td></td><td>-提供“编辑”、“删除”按钮，按钮状态根据用户权限动态显示</td></tr><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td></td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

#### 6.6.2.5. ⻔户管理员登录

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>门户管理员登录</td><td>优先級</td><td>关闭</td></tr><tr><td>需求描述</td><td colspan="5">1.登录流程： －支持用户名、密码登录方式。 －密码错误3次后锁定账号5分钟，需联系系统管理员解锁 2.Token 机制： -登录成功生成JWTToken，有效期24小时，过期前15分钟可自动续期</td></tr><tr><td>业务流程</td><td colspan="2">－单用户允许多个活跃会话，以支持多端访问 不涉及</td><td colspan="3"></td></tr><tr><td>输入輸出约束</td><td colspan="3">不涉及</td><td colspan="2"></td></tr><tr><td>验收标准</td><td colspan="3"></td><td colspan="2"></td></tr></table></body></html>

#### 6.6.2.6. ⻔户管理员退出

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>门户管理员退出</td><td>优先级</td><td>关闭</td></tr><tr><td>需求描述</td><td colspan="3">1.主动退出： -用户点击退出后，服务端销毁 Token 2.被动退出：</td><td></td><td></td></tr></table></body></html>

<html><body><table><tr><td></td><td>- token 过期，用户下次鉴权失败后跳转至登录页</td></tr><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td></td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

### 6.6.3. 门户用户管理

#### 6.6.3.1. 新增用户

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>新增用户</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">权限归属：系统管理员、门户管理员 1.系统管理员可新增门户用户，新增时必须绑定所属门户 2.门户管理员可新增门户用户，新增时将用户自动添加至当前门户管理员 3.门户用户信息包括： －用户名称 －登录密码</td></tr><tr><td>业务流程</td><td colspan="3">-所属门户管理员 不涉及</td><td></td><td></td></tr><tr><td>綸入输出约束</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan="3">用户新增成功</td><td></td><td></td></tr><tr><td>其它说明</td><td colspan="3">不涉及</td><td></td><td></td></tr></table></body></html>

#### 6.6.3.2. 编辑用户

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>编辑用户</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="3">权限归属：系统管理员、门户管理员 1.系统管理员可编辑门户用户，修改所属门户管理员 2.门户管理员可编辑门户用户，不可更改所属门户管理员</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan="3">3.可修改内容项：重置密码 不涉及</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan="3">用户密码重置成功，可以使用新密码登录</td><td></td><td></td></tr><tr><td>其它说明</td><td colspan="3">不涉及</td><td></td><td></td></tr></table></body></html>

#### 6.6.3.3. 删除用户

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>删除用户</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td>权限归属：系统管理员、门户管理员 1.系统管理员可删除门户用户 2.门户管理员可删除门户用户</td><td></td><td></td><td></td><td></td></tr><tr><td>业务流程</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan="3">用户删除成功</td><td></td><td></td></tr><tr><td>其它说明</td><td colspan="3">不涉及</td><td></td><td></td></tr></table></body></html>

#### 6.6.3.4. ⻔户用户列表

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>门户用户列表</td><td>优先级</td><td>高</td></tr></table></body></html>

<html><body><table><tr><td>需求描述</td><td>权限归属：系统管理员、门户管理员 1.列表展示： －分页展示所有门户用户，默认按创建时间倒序排列。 －显示字段：用户名、认证模式、所属门户管理员、状态（启用/禁用）、最后登录 时间。 2.筛选与搜索：</td></tr><tr><td></td><td>－提供“编辑”、“禁用/启用”、“删除”按钮，按钮状态根据用户权限动态显示</td></tr><tr><td>业务流程 輸入輸出約束</td><td>不涉及</td></tr><tr><td>验收标准</td><td>不涉及 用户列表显示成功，输入搜索条件，可显示符合条件的数据。</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

#### 6.6.3.5. ⻔户用户登录

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称门户用户登录</td><td></td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">1.登录流程： －支持用户名、密码登录方式。 -密码错误5次后锁定账号1分钟 －统一web 平台进行用户名、密码验证成功后，智能门户进行用户状态的鉴权，如 果用户处于禁用状态，提示联系管理员启用；如果处于启用状态，重定向到该用户 所属的门户首页 2.Token机制： -登录成功生成JWTToken，有效期半小时</td></tr><tr><td>业务流程</td><td>－单用户允许多个活跃会话，以支持多端访问 不涉及</td><td colspan="2"></td><td></td><td></td></tr></table></body></html>

<html><body><table><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td>启用状态用户登录成功；禁用状态用户登录后进行提示。</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

#### 6.6.3.6. ⻔户用户退出

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>门户用户退出</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="3">1.主动退出： -用户点击退出后，服务端销毁 Token 2.被动退出:</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan="3">- token 过期时，用户下次鉴权失败后跳转至登录页</td><td></td><td></td></tr><tr><td>輸入輸出约束</td><td colspan="3">不涉及 不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan="3">用户退出系统成功，返回登录页面。</td><td></td><td></td></tr><tr><td>其它说明</td><td colspan="3">不涉及</td><td></td><td></td></tr></table></body></html>

#### 6.6.3.7. 启用⻔户用户

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>启用门户用户</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">1．启用用户： -新增用户时，可以设置用户为启用状态，只有状态为“启用”的用户才能访问统一 门户 －支持从用户列表中批量选择用户，设置启用状态</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>綸入输出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">用户启用成功，可以登录。</td></tr></table></body></html>

<html><body><table><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

#### 6.6.3.8. 禁用⻔户用户

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>禁用门户用户</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">1.禁用用户： -新增用户时，可以设置用户为禁用状态，状态为“禁用”的用户无法访问统一门户 -支持从用户列表中批量选择用户，设置禁用状态</td></tr><tr><td>业务流程</td><td colspan="3">-被禁用的用户，无法登录统一门户，已有 Token 立即失效 不涉及</td><td colspan="2"></td></tr><tr><td>输入输出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">用户禁用成功，未登录的客户端登陆后进行提示；已登录的客户端进行提示。</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

### 6.6.4. 门户管理（Dify 实例管理）

#### 6.6.4.1. 创建⻔户

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称创建门户</td><td></td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td>权限归属：系统管理员 -门户名称 －首页配置：选择默认应用</td><td>1.系统管理员创建门户时，需填写： -门户标题、图标、logo、访问地址前缀、主题</td><td>－访问控制：匿名访问、授权访问（门户用户绑定该门户后才能访问）</td><td></td><td></td></tr></table></body></html>

<html><body><table><tr><td></td><td>3.门户状态：初始化中、初始化失败、运行中、已停止、启动中、异常、删除中、 删除失败</td></tr><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td>门户创建完成后，能通过门户访问地址进入门户</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

#### 6.6.4.2. 编辑⻔户信息

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>编辑门户信息</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="3">权限归属：系统管理员、门户管理员 1.修改门户名称、门户标题、图标、logo、主题</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan="3">用户编辑成功</td><td></td><td></td></tr><tr><td>其它说明</td><td colspan="3">不涉及</td><td></td><td></td></tr></table></body></html>

#### 6.6.4.3. ⻔户列表

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称门户列表</td><td></td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">权限归属：系统管理员 1.列表功能： -分页展示所有门户，显示字段：门户名称、状态（创建中/创建失败/运行中/已停 止/异常）、关联实例 -支持按状态、门户名称筛选 2.快捷操作：</td></tr></table></body></html>

<html><body><table><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入輸出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td>门户列表正常加载</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

#### 6.6.4.4. 启动⻔户

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称门户列表</td><td></td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">权限归属：系统管理员、门户管理员 1.启动流程 -在门户列表页操作“启动”门户 -后端自动拉起门户对应的 Dify 实例 后端更新门户状态为已启动、异常</td></tr><tr><td>业务流程</td><td colspan="3">-后端周期检测Dify实例的健康状态，连续3次检测失败则标记为"异常” 不涉及</td><td></td><td></td></tr><tr><td>输入輸出约束</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan="3">启动后门户状态变为运行中，且门户网站能正常访问</td><td colspan="2"></td></tr><tr><td>其它说明</td><td colspan="3">不涉及</td><td colspan="2"></td></tr></table></body></html>

#### 6.6.4.5. 停⽌⻔户

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>停止门户</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="3">权限归属：系统管理员、门户管理员 1.停止流程 -在门户列表页操作“停止”门户</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan="3">-后端自动停止门户对应的Dify实例，并将门户状态更新为已停止 不涉及</td><td></td><td></td></tr><tr><td>輸入輸出约束</td><td colspan="3">不涉及</td><td></td><td></td></tr></table></body></html>

<html><body><table><tr><td>验收标准</td><td>停止后门户状态变为已停止，且门户网站无法访问</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

#### 6.6.4.6. 删除⻔户

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>删除门户</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">权限归属：系统管理员 1.状态为创建中、运行中、删除中的门户不支持删除 2.删除流程： -在门户列表页操作“删除”门户 -前端给出二次确认 -后端开始删除门户，并将门户状态改为“删除中” -后端删除Dify实例对应的容器 －后端删除Dify 实例下产生的数据：应用、历史会话、知识库、数据库</td></tr><tr><td>业务流程</td><td colspan="3">不涉及</td><td colspan="2"></td></tr><tr><td>输入输出约束</td><td colspan="3">不涉及 门户删除后管理员无法看到门户信息，门户用户无法访问该门户，服务器上该门户</td><td colspan="2"></td></tr><tr><td>验收标准</td><td colspan="3">对应的Dify实例数据已删除</td><td colspan="2"></td></tr></table></body></html>

#### 6.6.4.7. 跳转管理端

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>跳转管理端</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">权限归属：系统管理员、门户管理员 1.在门户列表页点击门户右侧"跳转管理端”按钮，浏览器打开Dify管理端页面 2.免密登录：用户进入Dify管理端页面无需登录，平台自动以管理员身份访问Dify</td></tr><tr><td>业务流程</td><td colspan="5">管理端页面 不涉及</td></tr><tr><td>綸入输出約束</td><td colspan="5">不涉及</td></tr></table></body></html>

<html><body><table><tr><td>验收标准</td><td>浏览器能打开Dify管理端页面</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

#### 6.6.4.8. 跳转⻔户

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>跳转管理端</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">权限归属：系统管理员、门户管理员 1.在门户列表页点击门户右侧“跳转门户”按钮，浏览器打开对应门户的首页</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>输入输出約束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">浏览器能打开对应门户的首页</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

#### 6.6.4.9. ⾸⻚配置（迭代 2）

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>首页配置</td><td>优先级</td><td>低</td></tr><tr><td>需求描述</td><td colspan="5">权限归属：系统管理员、门户管理员 用户进入门户首页，默认展示一个问答式应用，用户可直接使用 1.平台拉取 Dify 实例中的现有问答式应用列表</td></tr><tr><td>业务流程</td><td colspan="5">2.选择某个应用保存后，用户访问门户时，首页中使用的应用即默认应用 不涉及</td></tr><tr><td>输入输出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5"></td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

6.6.4.10.访问控制配置（迭代 2）  

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>访问控制配置</td><td>优先级</td><td>低</td></tr><tr><td>需求描述</td><td colspan="3">权限归属：系统管理员、门户管理员 1.匿名访问：通过门户地址无需登录直接访问 2.授权访问：访问门户地址后，需要先登录才能访问</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan="3"></td><td></td><td></td></tr><tr><td>其它说明</td><td colspan="3">不涉及</td><td></td><td></td></tr></table></body></html>

### 6.6.5. Dify 容器化多实例部署改造

#### 6.6.5.1. Dify 多实例部署&网络隔离&目录隔离

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>Dify多实例部署&网络隔离&目 录隔离</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">1.基于Docker Compose 实现 Dify容器化部署 2.实现在一台宿主机上部署多个实例 3.每个实例使用独立的网络命名空间</td></tr><tr><td>业务流程</td><td colspan="5">4.每个实例使用独立的磁盘目录 不涉及</td></tr><tr><td>綸入綸出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">1.在门户管理端支持创建多个门户，在一体机中支持部署多套 Dify 实例 2.各Dify 实例中，知识库中的文档保存在宿主机中的独立目录中，不交叉存储</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

#### 6.6.5.2. 初始化供应商和模型

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>初始化供应商和模型</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="3">权限归属：系统管理员 1.Dify 部署好后，自动完成供应商和模型的初始化 2.模型信息，通过 SecuLlama API拉取</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>輸入输出约束</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan="5">用户创建好统一门后，Dify 实例自动部署，Dify 中的供应商和模型自动完成初始化， 内置应用无需人工配置即可正常使用</td></tr><tr><td>其它说明</td><td colspan="3">不涉及</td><td></td><td></td></tr></table></body></html>

#### 6.6.5.3. Dify 实例自动化部署

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td></td><td>需求名称Dify 实例自动化部署</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">权限归属：系统管理员 1.用户创建门户后，后台自动完成Dify实例的部署、初始化，无需人工干预 2.后台将Dify 实例的状态实时同步到门户中：初始化中、初始化失败、运行中、已</td></tr><tr><td>业务流程</td><td colspan="5">停止、启动中、异常、删除中、删除失败 不涉及</td></tr><tr><td>输入输出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">用户创建好统一门后，Dify实例自动部署，无需人工介入</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

### 6.6.6. 统一门户

#### 6.6.6.1. 访问⻔户⾸⻚

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>访问门户首页</td><td>优先级</td><td>高</td></tr></table></body></html>

权限归属：门户用户、门户管理员、系统管理员

1. 门户用户通过登录页登录后，自动跳转到门户首页

2. 门户管理员、系统管理员可在门户列表页中，点击后方的“跳转门户”，一键跳转到门户首页

3. 在门户的任意页面点击门户 logo 后，自动跳转到门户首页

![](images/4fc793f5592e3fa4db6e16307173bb733b2cbafa45bf3e95493e5ce6091b44c3.jpg)
```text
这张图片展示了一个名为“SANSEC智能助手”的界面，具体是“SANSEC探索版”。界面上方有一个导航栏，左侧列出了多个功能选项，包括新建会话、产品发布会策划方案讨论、市场营销数据分析、新功能开发建议、用户反馈问题解决、团队协作效率提升和竞品分析报告生成。右侧是一个聊天窗口，显示了与DeepSeek的对话，DeepSeek自我介绍并表示可以协助写代码、改文件、写作各种创意内容等任务。

图片下方有一个表格，标题为“需求描述”，列出了业务流程、输入输出约束、验收标准和其它说明，但这些部分的内容都标注为“不涉及”。这可能意味着当前的需求描述阶段还未涉及到具体的业务流程、输入输出约束、验收标准和其它详细说明。
```


#### 6.6.6.2. 用户信息展示

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>门户用户信息</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td></td><td>权限归属：门户用户、门户管理员、系统管理员</td><td>1.在统一门户任意页面固定位置，展示当前登录的用户信息，例如用户名称</td><td></td><td></td></tr></table></body></html>

<html><body><table><tr><td></td><td></td></tr><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入輸出約束</td><td>不涉及</td></tr><tr><td>验收标准</td><td></td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

#### 6.6.6.3. ⻔户⾸⻚对话

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称门户首页对话</td><td></td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">权限归属：门户用户、门户管理员、系统管理员 1.用户进入门户首页后，自动为用户展示门户默认的应用，用户可以直接使用该应 用进行交互式对话 2.门户首页应用支持用户查看在当前应用的会话历史列表，可选择某历史会话后继 续对话</td></tr><tr><td>业务流程</td><td colspan="3">不涉及</td><td></td></tr><tr><td>输入输出约束</td><td colspan="3">不涉及</td><td></td></tr><tr><td>验收标准</td><td colspan="3"></td></tr><tr><td>其它说明</td><td colspan="3">不涉及</td></tr></table></body></html>

#### 6.6.6.4. 应用探索（⻔户应用列表）

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称门户应用列表</td><td></td><td>优先级</td><td>高</td></tr><tr><td rowspan="2">需求描述</td><td colspan="5">权限归属：门户用户、门户管理员、系统管理员 1.用户进入探索页后，页面显示该门户对应 Dify 实例中的应用（应用探索页只展</td></tr><tr><td colspan="4">示已启用的应用） 2.应用按照标签分组展示，每个应用展示内容有应用名称、logo、应用简介</td></tr></table></body></html>

3. 标签来自Dify 管理后台中的预置数据和门户管理员修改的数据

4. 点击应用后跳转到应用对话页

5. 支持用户收藏某应用

<html><body><table><tr><td rowspan="3"></td><td>SANSEC智能助手</td><td colspan="5">Sarah Johntarn</td></tr><tr><td rowspan="2">十新建会话 Cti+K SANSEC探索版 批的直顶 长文生成据</td><td colspan="5">探索SANSEC+</td></tr><tr><td>有方推荐</td><td>办公效率 SANSEC探索版 问些太复奇？用保索强管慢看</td><td>社交媒乐 生活实用</td><td>长文生成器</td><td></td></tr><tr><td rowspan="3"></td><td>PPT助手 学术按索 代码助手</td><td>PPT助手 Kmi+APPT=生PPT</td><td></td><td>末SANSEO 口 学术接索 SEURPtO</td><td>你说一句，我写万字长文 学术问题来同理！</td><td>来RSNSEC</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>业务流程</td><td>不涉及</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>綸入輸出約束</td><td>不涉及</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>验收标准</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>其它说明</td><td>不涉及</td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

#### 6.6.6.5. 应用对话⻚

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称应用对话页</td><td></td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="3">权限归属：门户用户、门户管理员、系统管理员 1.用户点击快捷访问栏中的应用，进入应用对话页 2.在应用对话页，用户可以直接使用该应用进行交互式对话 3.应用会话页支持用户查看当前应用的会话历史列表，可选择某历史会话后继续对</td><td></td><td></td></tr></table></body></html>

![](images/e2cbfdb5b8f10029e38a200813d7be40ad3ba4d803e6b6f22cf360b9815385c6.jpg)
```text
这张图片展示了一个名为“SANSEC智能助手”的界面，该界面提供了多种功能模块，包括SANSEC探索版、长文生成器、PPT助手、学术搜索和代码助手。用户可以通过点击左侧的图标来选择不同的功能模块。右侧显示了当前选中的功能模块的内容，例如关于JVM默认堆大小的信息。此外，图片下方有一个表格，列出了业务流程、输入输出约束、验收标准和其它说明等项目，但这些项目的具体内容都标注为“不涉及”。
```


### 6.6.7. 预置应用

#### 6.6.7.1. 通用助手

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>通用助手</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td>通用智能助手，支持带有记忆的对话、支持上传文档进行问答</td><td></td><td></td><td></td><td></td></tr><tr><td>业务流程</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan="3">不涉及</td><td></td><td></td></tr></table></body></html>

<html><body><table><tr><td>验收标准</td><td></td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

#### 6.6.7.2. 图片转文字

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>图片转文字</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="3">上传图片，识别图中文字</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>输入輸出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5"></td></tr><tr><td>其它说明</td><td colspan="3">不涉及</td></tr></table></body></html>

#### 6.6.7.3. 英文翻译官

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>英文翻译官</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="3">支持中英文互相翻译</td><td colspan="2"></td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>输入輸出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5"></td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

#### 6.6.7.4. 文章润色

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>文章润色</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="3">选择需要的语言风格、字数，对原有文章进行润色</td><td></td><td></td></tr></table></body></html>

<html><body><table><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>輸入輸出約束</td><td>不涉及</td></tr><tr><td>验收标准</td><td></td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

#### 6.6.7.5. 图表生成器

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>图表生成器</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td></td><td>使用自然语言将数据或表格生成折线图、柱状图、饼图。</td><td></td><td></td><td></td></tr><tr><td>业务流程</td><td>不涉及</td><td></td><td></td><td></td><td></td></tr><tr><td>输入输出约束</td><td>不涉及</td><td></td><td></td><td></td><td></td></tr><tr><td>验收标准</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>其它说明</td><td>不涉及</td><td></td><td></td><td></td><td></td></tr></table></body></html>

#### 6.6.7.6. 英文全书翻译

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>英文全书翻译</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="3">英文全书翻译</td><td colspan="2"></td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>輸入輸出約束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5"></td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

#### 6.6.7.7. 公文写作

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>公文写作</td><td>优先级</td><td>高</td></tr></table></body></html>

<html><body><table><tr><td>需求描述</td><td>根据特定主题和内容生成15种法定格式的公文</td></tr><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td></td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

#### 6.6.7.8. 编码助手

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>编码助手</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="3">编程专家，可以实现多种编程语言的代码生成、智能注释、代码纠错、错误解析、 多语言转换等功能。</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan="3"></td><td></td><td></td></tr><tr><td>其它说明</td><td colspan="3">不涉及</td><td></td><td></td></tr></table></body></html>

#### 6.6.7.9. UI 设计助手

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>UI设计助手</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">根据自然语言描述生成html代码，可以点击预览</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>输入输出約束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5"></td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

#### 6.6.7.10. atex 公式编辑&识别

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>latex公式编辑&识别</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td>次上传一条公式），生成图中公式的latex代码。</td><td>支持自然语言描述生成 latex格式的公式并进行渲染；支持识别图片中的公式（每</td><td></td><td></td><td></td></tr><tr><td>业务流程</td><td>不涉及</td><td></td><td></td><td></td><td></td></tr><tr><td>输入输出约束</td><td>不涉及</td><td></td><td></td><td></td><td></td></tr><tr><td>验收标准</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>其它说明</td><td>不涉及</td><td></td><td></td><td></td><td></td></tr></table></body></html>

#### 6.6.7.11.Arxiv 论文助手

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>Arxiv论文助手</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">使用自然语言查询Arxiv网站上的论文</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>输入輸出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5"></td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

#### 6.6.7.12.数据库查询助手

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>数据库查询助手</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td></td><td>使用自然语言查询数据库，可以查询数据库结构和数据库中表的数据。需要使用 URI与数据库进行连接，支持 mysql、oracle、oraclellg、postgresql 或mssql。</td><td colspan="3"></td></tr><tr><td>业务流程</td><td colspan="3">不涉及</td><td colspan="2"></td></tr></table></body></html>

<html><body><table><tr><td>输入輸出約束</td><td>不涉及</td></tr><tr><td>验收标准</td><td></td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

### 6.6.7.13.shell 指令助手

<html><body><table><tr><td>需求编号</td><td>PR-F-6511</td><td>需求名称</td><td>shell指令助手</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="3">根据自然语言生成相应的shell 指令，并指定某个指定在目标服务器上执行 敏感指令会被拦截</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>输入輸出約束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5"></td></tr><tr><td>其它说明</td><td colspan="3">不涉及</td><td></td><td></td></tr></table></body></html>

#### 6.7. 数据围栏系统

为保障企业的数据安全和隐私安全，一体机提供一系列针对敏感数据的识别和处置方案，其中包括敏感数据识别算法，数据脱敏处理方式，业务自定义的配置选项和海量数据处理能力。能够应用多种隐私合规标准，对原始数据进行分级打标、判断敏感级别和实施相应的脱敏处理。

随着大语言模型在文本生成、对话交互等场景的广泛应用，其输出内容可能涉及政治、暴力、色情等敏感信息，存在合规风险。为满足法律法规及企业安全要求，需要在一体机内部构建内容审核组件，实时检测并拦截敏感内容，确保输出内容的安全性与合规性。

#### 6.7.1 数据脱敏组件

<html><body><table><tr><td>需求编号</td><td>PR-F-6701</td><td>需求名称</td><td>数据脱敏组件</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">数据脱敏组件需要提供对敏感数据进行识别和处置的能力。</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>输入输出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">不涉及</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

#### 6.7.2 内容审核组件

<html><body><table><tr><td>需求编号</td><td>PR-F-6702</td><td>需求名称</td><td>内容审核组件</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">内容审核组件需要对涉及政治、暴力、色情等敏感信息的内容进行检测和拦截。</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>输入输出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">不涉及</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

#### 6.7.3 组件集成

<html><body><table><tr><td>需求编号</td><td>PR-F-6703</td><td>需求名称</td><td>数据脱敏与内容审核组件集成</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="3">1.将数据脱敏组件与内容审核组件集成为数据围栏系统； 2.数据围栏系统以docker形式部署在一体机中； 3.数据围栏系统以restful api的方式提供服务；</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan="3">4.能够测试数据围栏服务是否正常工作； 不涉及</td><td></td><td></td></tr><tr><td>输入输出约束</td><td colspan="3">不涉及</td><td></td><td></td></tr><tr><td>验收标准</td><td colspan="3">1.可通过查看正在运行的容器，确认数据围栏系统已部署；</td><td></td><td></td></tr><tr><td>其它说明</td><td colspan="3">2.可通过发出restful api请求测试数据围栏系统能否正常工作； 不涉及</td><td></td><td></td></tr></table></body></html>

#### 6.7.4 数据围栏系统启用与禁用

<html><body><table><tr><td>需求编号</td><td>PR-F-6704</td><td>需求名称</td><td>数据围栏系统启用与禁用</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">1.预置应用默认启用数据围栏系统； 2.新建应用可以选择是否启用数据围栏系统；</td></tr><tr><td>业务流程</td><td colspan="5">3.已安装应用允许对数据围栏的启用和禁用进行修改； 不涉及</td></tr><tr><td>输入輸出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">1.预置应用的数据围栏系统开关默认开启； 2.新建应用时能够选择是否启用数据围栏系统；</td></tr></table></body></html>

<html><body><table><tr><td></td><td>3.能够对已安装应用的数据围栏系统进行启用与禁用；</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

## 6.8. 产品外观需求

<html><body><table><tr><td>需求编号</td><td>PR-F-6801</td><td>需求名称</td><td>服务器铭牌、外观需求</td><td>优先級</td><td>高</td></tr><tr><td>需求描述</td><td colspan="3">外购整机需要: 1、铭牌：粘贴铭牌 2、外观要求：粘贴公司相关商标、标签</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan="3"></td><td></td><td></td></tr><tr><td>輸入輸出約束</td><td colspan="3"></td><td></td><td></td></tr><tr><td>验收标准</td><td colspan="3">外观满足正常出货要求</td><td></td><td></td></tr><tr><td>其它说明</td><td colspan="3"></td><td></td><td></td></tr></table></body></html>

## 6.9. 接口需求

无。

6.10. 界面需求 无

## 6.11. 性能需求

不涉及

## 6.12. 可靠性/可用性需求

产品在规定的条件下，在规定的时间内，在一定条件下无故障的完成规定的功能的能力或可能性称为可靠性。对那些发生质量事故会造成巨大损失或危及人身、社会安全的产品，可靠性是使用过程中主要的质量指标。可靠性可以通过平均无故障时间、平均修复时间、平均失效时间等指标体现。

可用性是在某个特定时间，系统能够正常运行的概率或时间占有率期望值。通常使用N 个九来表示系统可用性。

如果该产品有国家或国际标准、行业标准可循，可以摘录标准中的可靠性/可用性相关要求内容，如完全遵守，可以直接写符合《\*\*\*》标准第\*章节安全性要求。

原则上产品需要满足公司发布的《产品安全基线需求》，并参考竞品有竞争力的可用性/可靠性能力，具体内容参照《产品安全基线》。

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td><td>验收标准</td></tr><tr><td>PR-R-0001</td><td>压测7天</td><td>压测7天能够稳定运行</td><td>低</td><td>压测正常</td></tr></table></body></html>

注：

1． 需求编号采用PR-R-\*\*\*\*的形式，PR 代表产品，R 代表可靠性需求，编号从0001开始依次累加。  
2． 填写表格时需要按照需求的层次自行增加子章节。

## 6.13. 安全性需求

保护产品的要素，以防止各种非法的访问、使用，修改、破坏或者泄密。比如：

1. 利用可靠的密码技术。

2. 掌握特定的记录或历史数据集。

3. 给不同的模块分配不同的功能。

4. 限定一个程序中某些区域的通信。

5. 计算临界值的检查和。

如果该产品有国家或国际标准、行业标准可循，可以摘录标准中的安全性要求内容，如完全遵守，可以直接写符合《\*\*\*》标准第\*章节安全性要求。

原则上产品需要满足公司发布的《产品安全基线需求》，具体内容参照《产品安全基线》。

注：

1． 需求编号采用PR-S-\*\*\*\*的形式，PR 代表产品，S 代表安全需求，编号从0001开始依次累加。  
2． 填写表格时需要按照需求的层次自行增加子章节。

### 6.13.1 知识库加密

<html><body><table><tr><td>需求编号</td><td>PR-S-0001</td><td>需求名称</td><td>知识库加密</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">实现对知识库中所有文档原始文件的透明加密： 1.用户对知识库文档上传、文档搜索、内容搜索、内容编辑、删除等操作均为无感 操作</td></tr><tr><td>业务流程</td><td colspan="5">2.仅授权用户（包括用户从属进程）才能访问文档原始文件</td></tr><tr><td>输入输出约束</td><td colspan="5">不涉及 不涉及</td></tr><tr><td>验收标准</td><td colspan="5">知识库加密动作不影响用户对知识库的操作</td></tr><tr><td>其它说明</td><td colspan="5">非授权用户无法访问知识库中的原始文档 不涉及</td></tr></table></body></html>

## 6.14. 可维护性需求

所有提高可维护性或可扩展性的需求。如使用行业标准，编码标准，开放式结构，可兼容语言，备份机复原，数据交换，命名约定，类库，维护访问，维护工具等。可以从维护者角度考虑，产品应该提供的错误码反馈、日志记录、自我诊断等功能。

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td><td>验收标准</td></tr><tr><td>PR-M-0001</td><td>服务端日志</td><td>在系统发生运行故障，或运算错误等情 况，需要有日志记录，能够定位问题。</td><td></td><td></td></tr></table></body></html>

注：

1． 需求编号采用PR-M-\*\*\*\*的形式，PR 代表产品，M 代表可维护性需求，编号从0001 开始依次累加。  
2． 填写表格时需要按照需求的层次自行增加子章节。

## 6.15. 工作状态需求

工作状态需求是指使用本硬件产品的客户或者是本公司其他部门的同事，能够通过硬件设备上的一些输出状态能够及时的获取硬件设备的工作情况，同时在硬件设备出现异常或作出其他反应时，用户能够及时针对特定情况做出反应。

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td><td>验收标准</td></tr><tr><td>PR-WS-0001</td><td></td><td></td><td></td><td></td></tr><tr><td>PR-WS-0002</td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

注：

1．需求编号采用PR-WS-\*\*\*\*的形式，PR 代表产品，WS 代表工作状态，编号从0001开始依次累加。

2．填写表格时需要按照需求的层次自行增加子章节。

## 6.16. 结构需求

硬件设备的设计开发过程涉及到硬件设备外观和结构方面的设计，一般硬件设备的结构尺寸会直接决定其应用场景，因此在需求阶段应该制定好硬件设备的结构方面的需求，可使用行业标准、相关政府标准等结构设计。

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-ST-0001</td><td>密码卡 扩展槽</td><td>外购4卡/8卡整机，在2网卡、1RAID 卡之外，需要额外的PCIE卡槽安装 62 型密码卡。</td><td>高</td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr></table></body></html>

注：

1．需求编号采用PR-ST-\*\*\*\*的形式，PR 代表产品，ST 代表结构需求，编号从0001开始依次累加。  
2．填写表格时需要按照需求的层次自行增加子章节。

## 6.17. 环保需求

环保相关的需求，一般涉及到硬件部件或者整机对环境、人体等影响的需求，例如要求硬件材料无铅等。

无特殊要求，依赖供应商的产品规格。

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-E-0001</td><td></td><td></td><td></td></tr><tr><td>PR-E-0002</td><td></td><td></td><td></td></tr></table></body></html>

注：

需求编号采用PR-E-\*\*\*\*的形式，PR 代表产品，E 代表认证需求，编号从0001 开始依次累加。

填写表格时需要按照需求的层次自行增加子章节。

## 6.18. 认证需求

产品在销售到特定的国家、地区或者行业之前，由第三方机构进行的准入检测，验证产品是否能满足国家、行业等相关技术标准。如：国密局产品型号认证、国际FIPS认证、公安部销售许可证申请、中国CCC 认证，欧盟的CE 认证等。

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>认证等级</td><td>接口规范</td><td>优先级</td></tr><tr><td>PR-C-0001</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>PR-C-0002</td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

注：

1. 需求编号采用PR-C-\*\*\*\*的形式，PR 代表产品，C 代表认证需求，编号从0001开始依次累加。  
2. 填写表格时需要按照需求的层次自行增加子章节。

## 6.19. 用户文档需求

用户指南、联机帮助、安装指南、配置文件等。

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描 述</td><td>优先级</td></tr><tr><td>PR-D-0001</td><td>用户指南v1.0.0</td><td></td><td></td></tr></table></body></html>

注：

需求编号采用PR-D-\*\*\*\*的形式，PR 代表产品，D 代表文档需求，编号从0001开始依次累加。

填写表格时需要按照需求的层次自行增加子章节。

## 6.20. 客户特殊需求

指客户提出以上需求以外的特殊需求。  

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td><td>验收标准</td></tr><tr><td>PR-CS-0001</td><td></td><td></td><td></td><td></td></tr><tr><td>PR-CS-0002</td><td></td><td></td><td></td><td></td></tr></table></body></html>

## 6.21. 法律法规要求

描述产品需遵循的相关法律法规。  

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-LR-0001</td><td>中华人民共和国密码法</td><td>满足相关要求</td><td>高</td></tr><tr><td>PR-LR-0002</td><td>计算机信息网络国际联网 安全保护管理办法</td><td>满足相关要求</td><td>高</td></tr><tr><td>PR-LR-0003</td><td>网络安全技术生成式人 工智能服务安全基本要求 (TC260-003)</td><td>满足相关要求</td><td>高</td></tr></table></body></html>

注：以上所列通用法律法规，若本产品不满足或有其他标准，请自行修改或添加。

## 6.22. 国家及行业标准要求

描述产品需遵循的国家及行业标准要求。

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-NIS-0001</td><td>GB-T8567-2006计算机 软件文档编制规范</td><td>满足相关要求</td><td>高</td></tr><tr><td>PR-NIS-0002</td><td>GB-T9386-2008计算机 软件测试文档编制规范</td><td>满足相关要求</td><td>高</td></tr></table></body></html>

注：以上所列行标，若本产品不满足或有其他标准，请自行修改或添加。

## 6.23. 失效模式分析(参见《设计失效模式和影响分析(DFMEA)库》)

## 6.24. 其他需求

其他以上未涉及的需求，如使用寿命、可用性、经济性、易用性、效率、可移植性等。如果有明确需要，可在此处添加，否则可裁剪。

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-O-0001</td><td></td><td></td><td></td></tr><tr><td>PR-O-0002</td><td></td><td></td><td></td></tr></table></body></html>

注：

1． 需求编号采用PR-O-\*\*\*\*的形式，PR 代表产品，O 代表其他需求，编号从0001开始依次累加。

2． 填写表格时需要按照需求的层次自行增加子章节。