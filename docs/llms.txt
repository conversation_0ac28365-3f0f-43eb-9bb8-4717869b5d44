# (AI UI Automation, AI Testing, Computer Use, Browser Use, Android Use)

> AI UI Automation, AI Testing, Computer Use, Browser Use, Android Use. It offers JavaScript SDK, Chrome extension, and support for scripting in YAML.

## Other

- [API Reference](/api.md)
- [Automate with <PERSON><PERSON>ts in YAML](/automate-with-scripts-in-yaml.md)
- [Introducing Instant Actions and Deep Think](/blog-introducing-instant-actions-and-deep-think.md)
- [Use JavaScript to Optimize the AI Automation Code](/blog-programming-practice-using-structured-api.md)
- [Support Android Automation](/blog-support-android-automation.md)
- [Bridge Mode by Chrome Extension](/bridge-mode-by-chrome-extension.md)
- [Caching](/caching.md)
- [Changelog](/changelog.md)
- [Choose a Model](/choose-a-model.md)
- [](/common/prepare-android.md)
- [](/common/prepare-key-for-further-use.md)
- [](/common/setup-env.md)
- [](/common/start-experience.md)
- [Data Privacy](/data-privacy.md)
- [FAQ](/faq.md)
- [Integrate with Android (adb)](/integrate-with-android.md)
- [Integrate with Playwright](/integrate-with-playwright.md)
- [Integrate with Puppeteer](/integrate-with-puppeteer.md)
- [LLMs.txt Documentation](/llm-txt.md)
- [MCP Server](/mcp-android.md)
- [Config Model and Provider](/model-provider.md)
- [Prompting Tips](/prompting-tips.md)
- [Quick Experience with Android](/quick-experience-with-android.md)
- [Quick Experience by Chrome Extension](/quick-experience.md)
- [MCP Server](/web-mcp.md)