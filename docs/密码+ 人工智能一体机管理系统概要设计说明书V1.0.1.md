用户名称密级：

# <密码 $+$ 人工智能一体机管理系统>产品概要设计说明书

（V1.0.0）

项目名称： 密码 $^ +$ 人工智能一体 项目编号：机管理系统

编    写： AI 实验室 编写日期： 2025-04-09

审    核： 评审专家 审核日期：

三未信安科技股份有限公司

SWXA-CMMI-SD-30

AI 实验室

2025 年 04 月 09 日

文档修订记录  

<html><body><table><tr><td>版本</td><td>修改内容</td><td>修改人</td><td>修改日期</td><td>审核人</td><td>发布日期</td></tr><tr><td>V1. 0. 0</td><td>创建</td><td>万军</td><td>2025.04.09</td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

各版本修改的功能点清单

<html><body><table><tr><td>版本</td><td>功能点</td><td>章节</td><td>修改日期</td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr></table></body></html>

目录  
第一章 引言 ..1.1 编写目的 .1.2 预期读者 .1.2.1 名称及版本号 .1.2.2 与其它系统的关系 ....1.3 术语和缩写 .1.4 参考资料 ..  
第二章 系统概述 .... . 92.1 系统目标 ... . 92.2 设计原则 ....2.3 运行环境 . . 92.3.1 硬件平台 .... ... 92.3.2 软件平台 .. 102.4 应用软件整体结构概述 12  
第三章 SS-01 安全大模型运维管理系统 .. .... 143.1 模块划分 .... . 143.2 重要流程说明 . . 143.3 SS-01-01 服务管理 ... . 153.3.1 功能点清单 . ... 153.3.2 重要流程说明 .. . 153.3.3 SS-01-01-01 服务启动 . . 153.3.4 SS-01-01-02 服务停止 ... . 183.3.5 SS-01-01-03 服务状态查看 . .... 193.3.6 SS-01-01-04 服务版本查看 ... . 203.3.7 SS-01-01-05 服务基本信息查看 . ... 203.4 SS-01-02 模型管理 ... . 213.4.1 功能点清单 . .... 223.4.2 重要流程说明 .... .... 233.4.3 SS-01-02-01 查询模型列表 . 233.5 SS-01-03 监控管理 .. . 243.5.1 功能点清单 ..... 243.5.2 重要流程说明 ... . 243.5.3 SS-01-03-01 监控面板 .. 243.6 SS-01-04 安全认证 ..... .. 263.6.1 功能点清单 .. ..... 273.6.2 重要流程说明 .. . 273.6.3 SS-01-04-01 Ukey 登录 .. .. 273.7 SS-01-05 监控组件 .... .. 283.7.1 功能点清单 . .... 303.7.2 重要流程说明 .... ... 303.7.3 SS-01-05-01 ..... ... 303.8 SS-01-06 字典模块 .. 31

3.8.1 功能点清单. 31

3.8.2 重要流程说明 . . 31  
3.8.3 SS-01-06-01 查询字典信息 . ... 31  
第四章 数据库设计 .. .. 32  
4.1 数据库表清单 . .. 93  
4.2 数据库表之间的关系 ... ...... 96  
4.3 实体集列表 . 97  
4.4 存储过程及函数 . . 错误!未定义书签。  
第五章 用户界面设计 .. . 98  
5.1 基本原则 . . 98  
5.2 设计概述 ... ... 98  
第六章 性能设计 .... .. 98  
第七章 安全设计 .. .. 103  
7.1 敏感信息安全传输 . . 103  
7.2 认证鉴权 . .. 105  
7.3 协议与接口防攻击 . .. 105  
7.4 产品开发、发布和安装安全 ... . 105  
7.5 Web 系统安全 ..... 105  
7.6 系统管理和维护安全 .. . 105  
7.7 WEB 安全 .. .. 106  
7.8 代码扫描和病毒扫描 .. . 106  
7.9 合法监听接口 . . 106  
7.10 隐私保护 .... . 106  
7.11 未公开接口 .. . 106  
7.12 访问通道控制 .. ... 106  
7.13 软件完整性保护 . . 107  
7.14 敏感数据与加密保护 . 107  
7.15 日志审计 ... . 错误!未定义书签。  
7.16 权限最小化 .. 107  
7.17 口令要求 ... ..... 107  
第八章 稳定性设计 ... .. 108  
第九章 异常处理 .. .. 109  
9.1 异常信息设计 . .. 109  
9.2 异常情况处理 . .. 109  
第十章 附录与规范 .. . 110  
10.1 附录 1（组件清单） 110  
10.2 附录 2（端口清单） . 110

# 第一章 引言

### 1.1 编写目的

本文档描述密码 $^ +$ 人工智能一体机管理系统 $\cdot +$ 智能应用统一门户的相关功能设计，用于指导开发、测试。

### 1.2 预期读者

本文档的阅读对象是软件开发人员、业务规范设计人员、软件测试人员、系统安装人员及用户代表。并为系统测试人员提供测试依据。

## 1.2.1 名称及版本号

密码 $^ +$ 人工智能一体机管理系统 CryptoAI OneSystem V1.0.0。

智能应用统一门户 AI Unified Portal V1.0.0

## 1.2.2 与其它系统的关系

人工智能一体机管理系统是基于公司 AI 一体机硬件，实现对运行框架以及大模型的管理，属于自研新系统，与其他系统不存在关联关系。

智能应用统一门户，基于统一 web 平台实现用户、组织、权限的管理，基于开源的大语言模型应用开发平台Dify 为用户提供各种类型的AI 应用工具。

### 1.3 术语和缩写

<html><body><table><tr><td>编号</td><td>名词</td><td>说明</td></tr><tr><td></td><td>CryptoAI OneSystem</td><td>密码+人工智能一体机管理系统</td></tr><tr><td></td><td>secuLlama</td><td>基于Ollama 添加安全访问策略之后的大模型运行框 架。</td></tr><tr><td></td><td>vLLM</td><td>Python库，大模型推理引擎。</td></tr></table></body></html>

<html><body><table><tr><td>Dify</td><td></td><td>Dify 是一款开源的大语言模型(LLM)应用开发平 台。它融合了后端即服务（Backend as Service）和 LLMOps 的理念，使开发者可以快速搭建生产级的 生成式 AI 应用</td></tr><tr><td></td><td>知识库</td><td>知识库（Knowledge）是一系列文档（Documents） 的集合，一个文档内可能包含多组内容分段 （Chunks），知识库可以被整体集成至一个应用中 作为检索上下文使用，用户可以将企业内部文档、 FAQ、规范信息等内容上传至知识库，知识库会自动 进行结构化处理。当LLM接收到用户的问题后，将 首先基于关键词在知识库内检索内容。知识库将根据 关键词，召回相关度排名较高的内容区块，向LLM</td></tr><tr><td></td><td>应用</td><td>提供关键上下文以辅助其生成更加精准的回答 应用是指基于GPT等大语言模型构建的实际场景 应用。通过创建应用，用户可以将智能AI技术应 用于特定的需求，应用类型包括：聊天助手、文档生 成应用、Agent、对话流、工作流。</td></tr><tr><td></td><td>聊天助手应用</td><td>基于LLM 构建对话式交互的助手，通过交互式界 面，采用一问一答模式与用户持续对话，可以用在客 户服务、在线教育、医疗保健、金融服务等领域。这 些应用可以帮助组织提高工作效率、减少人工成本和 提供更好的用户体验。</td></tr><tr><td></td><td>文本生成应用</td><td>面向文本生成类任务的助手，例如撰写故事、文本分 类、翻译等</td></tr><tr><td></td><td>Agent应用</td><td>能够分解任务、推理思考、调用工具的对话式智能助 手</td></tr><tr><td></td><td>对话流应用</td><td>适用于定义等复杂流程的多轮对话场景，具有记忆功 能的应用编排方式</td></tr><tr><td></td><td>工作流应用</td><td>适用于自动化、批处理等单轮生成类任务的场景的应 用编排方式</td></tr></table></body></html>

### 1.4 参考资料

# 第二章 系统概述

### 2.1 系统目标

实现对AI 一体机的运维管理功能，方便运维人员通过页面实现对大模型、模型运行框架的管理与监控，避免通过后台命令行引起的误操作。

实现对AI 一体机内的知识库、用户输入以及大模型输出内容的数据脱敏与数据围栏，提供一系列针对敏感数据的识别和处置方案，降低人工审核成本，提升内容质量。

为 AI 应用提供聚合访问入口，基于客户组织灵活实现门户访问权限隔离、应用访问权限隔离、知识库使用权限隔离。

实现对门户的运维管理功能，对应用的开发、编辑、维护功能，对知识库的编辑功能。

### 2.2 设计原则

系统采用模块化设计，模块之间减少耦合度，模块之间定义清晰的 API 接口。

各个模块的设计尽量遵循相关技术规范或标准。

在公司范围内有可复用的组件时，必须采用可复用组件，减少重复开发。

### 2.3 运行环境

## 2.3.1 硬件平台

硬件配置：  

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td rowspan="2">PR-E-0001</td><td rowspan="2">AI一体机整 体配置</td><td>CPU: Intel Xeon Gold 6430 32 核</td><td>高</td></tr><tr><td>64线程*2 内存：32GB DDR4 RECC *8 硬盘1:480G SATA *2</td><td></td></tr></table></body></html>

<html><body><table><tr><td></td><td></td><td>GPU: GeForce RTX 4090 24GB *4 电源：2000W*2 Ukey : XT200*3 安全卡:SanSec TDEC（Trusted Data Encryption Card）*1 密码卡：版本待定？？</td></tr></table></body></html>

## 2.3.2 软件平台

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-E-0001</td><td>操作系统</td><td>Ubuntu 22.04.4 LTS</td><td>高</td></tr><tr><td>PR-E-0002</td><td>JAVA库</td><td>OpenJDK 17</td><td>高</td></tr><tr><td>PR-E-0003</td><td>Nginx</td><td>sansec/3.20.2</td><td>高 高</td></tr><tr><td></td><td></td><td>Ukey C0S 版本：V2.4.14 Ukey控件版本：3.1.4 使用公用组件JCE-5.3.3.22版本； 监控组件使用2.3.2版本-最新稳定版 本</td><td></td></tr><tr><td></td><td>Docker</td><td>虚拟化容器运行引擎，用于运行1lm 服务、dify 服务、管理系统进程。</td><td></td></tr><tr><td></td><td>python</td><td>ShieldLM运行环境，python3.10+</td><td>高</td></tr><tr><td></td><td>go</td><td>godlp运行环境，用于 secuLlama开发。</td><td>高</td></tr><tr><td></td><td>CUDA toolkit</td><td>英伟达并行计算平台和编程模型工具 包</td><td></td></tr><tr><td></td><td>Mindie</td><td>基于昇腾硬件的高性能AI推理引擎</td><td></td></tr></table></body></html>

## 2.3.3 开源组件

<html><body><table><tr><td>名称</td><td>说明</td><td>开源协议</td></tr></table></body></html>

<html><body><table><tr><td>VLLI 0.8.5</td><td>ython库 人模型推理引挚 在本产品中用于提供兼容 openAI 大模型 API服务。</td><td>Apacie2.0</td></tr><tr><td>secuLlm</td><td>可能有性能优化方面的代码级修改。 实现vLLm 多进程服务管理功能及安全加 固。 在本产品中用于多模型服务管理、模型服 务 API鉴权等。</td><td>Apache 2.0</td></tr><tr><td>Dify v1. 3. 1</td><td>开源的大语言模型(LLM）应用开发平台。 在本产品中用于 AI 应用的开发和服务运」附加说明：1.不能多租户 行、知识库构建和使用。 无代码修改。</td><td>Apache 2.0 使用：单实例多工作空 间。 2.不能修改 logo和 copyright。</td></tr><tr><td>springboot 3.4.6</td><td>Java Web 应用开发框架。 在本产品中用于WEB应用开发与运行。 无代码修改。</td><td></td></tr><tr><td>spring gateway 3. 1.3</td><td>API 网关组件。 在本产品中用于基础网关。 无代码修改。</td><td></td></tr><tr><td>redis v6-alpine PostgreSQL</td><td>内存数据库。 在本产品中用于Dify 数据存储。 无代码修改 关系型数据库。</td><td>MIT PostgreSQL License</td></tr><tr><td>v15-alpine</td><td>在本产品中用于 Dify 数据存储和统一门</td><td></td></tr><tr><td>weaviate v1. 19.0</td><td>户数据存储。 无代码修改。 向量数据库。 在本产品中用于知识库存储。</td><td>BSD-3-Clause</td></tr></table></body></html>

<html><body><table><tr><td></td><td>无代码修改。</td><td></td></tr><tr><td>godlp v1. 2. 15</td><td>敏感数据识别组件 在本产品中用于对大模型输出内容 进行敏感数据识别与处置。 无代码修改。</td><td>MIT</td></tr><tr><td>ShieldLM-14B-qwen</td><td>安全检测工具 在本产品中帮助检测大型语言模型生成内 容中的安全问题。它符合一般的人类安全 标准。 无代码修改。</td><td>MIT</td></tr></table></body></html>

## 2.4 应用软件整体结构概述

三未信安基于密码安全与 AI 技术的深厚积累，针对大模型本地化部署中的安全挑战，倾力打造"密码+"人工智能一体机。

产品深度融合国产硬件、大模型 RAG 框架及全栈式安全防护能力，围绕“硬件 $^ { + }$ 软件 $+$ 服务”提供身份认证、数据隔离、内容过滤、模型保护等核心功能模块。面向各行业智能化升级需求，形成集智能问答交互、文档分析处理、数据治理优化等多功能于一体的“开箱即用”解决方案。通过对算力资源、算法模型与安全机制的统筹设计，全面兼顾业务效率与合规要求，真正实现模型部署易、管理省、安全强，让企业在智能化转型中稳步前行。

密码 $^ { + }$ 人工智能一体机管理系统用于管理、监控 AI 一体机，利用该系统，可以将后台执行的功能通过页面来提供，方便客户使用、管理、监控一体机。该管理系统的整体架构图如下，核心为应用层：

![](images/52600cd366c358f8527e0f385111053758ec38fb1c280f69a738c30dbc89cf00.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个系统的架构图，从用户接入到硬件层的各个组成部分。以下是对图片内容的详细描述：

## 用户接入
- **浏览器**、**移动app**、**社交应用**、**API Client** 和 **第三方应用** 都可以通过不同的方式接入系统。

## 应用层
- **智能助手**、**工作流** 和 **知识库** 是Dify实例1的一部分。
- Dify实例2和其他未命名的应用也存在于这一层。
- **数据围栏** 用于隔离和保护数据。

## 智能应用统一门户
- 包括 **基础网关**、**Dify实例管理**、**统一门户**、**应用市场**、**用户权限管理** 和 **围栏管理** 等功能。

## 模型层
- **SecuLlama** 和 **VLLM管理组件** 是两个主要的模型管理模块。
  - **SecuLlama** 包含 **LLM API网关**、**模型安全**、**输出安全** 和 **模型推理** 功能，并支持 **DeepSeek**、**Qwen系列** 和 **Llama系列** 等模型。
  - **VLLM管理组件** 同样包含 **LLM API网关**、**模型安全**、**输出安全** 和 **模型推理** 功能，并支持 **VLLM1 DeepSeek**、**VLLM1 Qwen系列** 和 **VLLM3 Llama系列** 等模型。

## 一体机管理系统
- 提供 **服务管理**、**模型管理**、**监控面板** 和 **显卡面板** 等功能。

## 系统层
- 包括 **驱动**、**CUDA/CANN**、**数据库**、**向量数据库** 和 **知识库文件透明加密** 等组件。
- 运行在 **操作系统OS** 上。

## 硬件层
- 包括 **GPU/NPU**、**NIC**（网络接口控制器）、**DISK**（磁盘）、**CPU** 和 **密码卡** 等硬件设备。

## 监控组件
- 用于监控系统的运行状态和性能。

这张架构图展示了从用户接入到硬件层的完整系统结构，涵盖了应用、模型、系统和硬件等多个层面的功能和组件。
```


说明：管理系统不涉及利用大模型进行推理、会话等业务，仅用来提供运维功能。具体的业务管理、运营功能敬请期待后续产品。

### 2.5 部署架构

"密码+"人工智能一体机产品采用软硬件一体化部署模式，所有服务进程以Docker 容器方式部署，其部署架构示意如下：

![](images/c0710e915eab65d9af861db2c37adb585985e5e8b72fc234b8b8d6ad1fa362c4.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个系统架构图，主要描述了一体机中各个组件之间的关系和功能。以下是图片内容的详细描述：

1. **一体机**：这是整个系统的总称，包含了所有的组件和模块。

2. **安全大模型运维管理系统**：这是一个管理系统，负责管理和维护安全大模型。它与secuLLM有直接的管理关系。

3. **智能应用统一门户**：这是一个统一的入口，用于管理和配置智能应用。它与secuLLM、postgresql、Dify...等组件有直接的关系。

4. **数据围栏**：这是一个虚拟的边界，包含godlp和ShieldLM两个组件，用于保护数据的安全。

5. **secuLLM**：这是一个核心组件，与vLLM、postgresql、Dify...等组件有直接的关系，负责管理和处理各种任务。

6. **vLLM**：这是一个具体的实现，有两个实例，都与secuLLM有管理关系。

7. **postgresql**：这是一个数据库组件，与secuLLM、Dify...等组件有存储关系。

8. **redis**：这是一个缓存组件，与postgresql有存储关系。

9. **weaviate**：这是一个数据存储和检索组件，与postgresql有透明加密关系。

10. **KMS+文件加密**：这是一个加密组件，与weaviate有透明加密关系。

这个架构图展示了各个组件之间的依赖关系和功能，帮助理解整个系统的运作方式。
```


其中安全大模型运维管理系统、智能应用统一门户、数据围栏为本次迭代自研组件， $^ { \prime \prime } { \sf K M S } +$ 文件加密”复用现有组件，其他为开源组件。

每个组件以docker 容器化部署运行并提供服务。

安全大模型运维管理系统、智能应用统一门户、secuLlm 可提供外部端口访问，其他组件都是内部端口。

高可用部署模式，需要 postgresql、weaviate、redis 实现双机集群模式部署（迭代 2 实现）。

# 第三章 SS-01 安全大模型运维管理系统

总体设计原则：基于 AI 一体机现有的功能与框架，按照低耦合、轻量化、可扩展的原则，完成对安全大模型运维管理系统的设计与开发。

### 3.1 模块划分

<html><body><table><tr><td>功能点编号</td><td>功能点清单</td><td>优先级</td></tr><tr><td>SS-01-01</td><td>服务管理</td><td>高</td></tr><tr><td>SS-01-02</td><td>模型管理</td><td>高</td></tr><tr><td>SS-01-03</td><td>监控管理</td><td>高</td></tr><tr><td>SS-01-04</td><td>安全认证</td><td>高</td></tr><tr><td>SS-01-05</td><td>监控组件</td><td>高</td></tr><tr><td>SS-01-06</td><td>字典模块</td><td>高</td></tr><tr><td>SS-01-07</td><td>知识库安全管理</td><td>高</td></tr><tr><td>SS-01-08</td><td>apikey 管理</td><td>高</td></tr></table></body></html>

### 3.2 重要流程说明

### 3.3 SS-01-01 服务管理

## 3.3.1 功能点清单

<html><body><table><tr><td>功能点编号</td><td>功能点清单</td><td>优先级</td></tr><tr><td>SS-01-01-01</td><td>服务启动</td><td>高</td></tr><tr><td>SS-01-01-02</td><td>服务停止</td><td>高</td></tr><tr><td>SS-01-01-03</td><td>服务状态查看</td><td>高</td></tr><tr><td>SS-01-01-04</td><td>服务版本查看</td><td>高</td></tr><tr><td>SS-01-01-05</td><td>查看服务基本信息</td><td>高</td></tr><tr><td>SS-01-01-06</td><td>下载操作手册</td><td>高</td></tr></table></body></html>

## 3.3.2 重要流程说明

### 3.3.3 SS-01-01-01 服务启动

### ******* 功能描述

通过页面实现对LLM 运行框架服务的启动，针对当前AI 一体机，面向的是secuLlama服务，后续如果替换为vLLM，可能面向的是vLLM 封装之后的服务。

只有服务启动之后，才能看到服务的状态、版本，以及运行模型的信息。

支持传入参数启动模型：结合当前的启动脚本，支持的参数有监听的 IP、端口、是否配置 ssl（tls/gmtls/none）、HMAC key 配置、模型位置配置，为了能够快速实现一版该功能，当前版本仅显示默认的参数，不允许修改，后续待 vLLM 研究结束之后，再考虑具体的参数。

默认值：监听的ip：0.0.0.0，端口：11434，是否配置 SSL：none(不配置)，模型位置：\~/.ollama/models/

### ******* 处理流程

![](images/45120becd3ee2b254b8041b06c431df133eefbd2dc099d34ecee4245d91ca3c5.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个服务配置界面，具体信息如下：

- **服务名称**: secuLlama
- **监听IP**: 0.0.0.0
- **端口**: 11434
- **协议配置**: none
- **模型位置**: ~/.ollama/models/
- **服务状态**: 运行/停止
- **服务版本**: 0.0.0

在界面底部有三个按钮：
- **启动**: 用于启动服务。
- **刷新状态**: 用于刷新服务状态。
- **停止**: 用于停止服务。

此外，还有一条提示信息：“服务版本和服务状态需要在服务运行时查看”，以及“服务状态运行时，启动不可用；服务状态停止时，停止不可用”。
```


![](images/43f6906abcd2b2035da8aff8f9b04e77b5ac52762f0b844f86a2f198de1ab866.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张流程图，描述了一个服务启动和运行的逻辑过程。流程从“开始”节点开始，然后检查“服务运行中？”如果服务已经在运行（“是”），则直接进入“结束”节点；如果服务没有在运行（“否”），则执行“启动服务”操作，之后再次检查“服务运行中？”。如果此时服务已经成功启动并运行（“是”），则进入“结束”节点；如果服务仍未运行（“否”），则执行“报错”操作，并最终进入“结束”节点。这个流程图展示了服务启动过程中可能出现的情况及其处理方式。
```


### 3.3.3.3 接口说明

接口 URI：/ai/ops/serve/start

请求方式：post

请求参数：

Body：  

<html><body><table><tr><td>参数名称</td><td>参数值</td><td>是否必须</td><td>实例</td><td>备注</td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

返回数据：  

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td></td><td>香</td><td></td></tr></table></body></html>

### 3.3.4 SS-01-01-02 服务停止

### 3.3.4.1 功能描述

通过页面实现对LLM 运行框架服务的停止，针对当前AI 一体机，面向的是secuLlama服务，后续如果替换为vLLM，可能面向的是vLLM 封装之后的服务。

### 3.3.4.2 处理流程

![](images/1188c9513f9878dd1fcacf1d38f628c9e46646e496a9ea9264f15b21c833ae1c.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张流程图，描述了一个服务停止的逻辑过程。流程从“开始”节点开始，首先判断“服务运行中？”如果答案是“否”，则直接进入“报错”步骤；如果答案是“是”，则执行“停止服务”操作，之后再次判断“服务运行中？”如果此时的答案仍然是“是”，则同样进入“报错”步骤；如果答案变为“否”，则流程结束。这个流程图展示了在尝试停止一个正在运行的服务时可能遇到的情况和处理方式。
```


### 3.3.4.3 接口说明

接口 URI：/ai/ops/serve/stop  
请求方式：post  
请求参数：  
Body：

返回数据：  

<html><body><table><tr><td>参数名称</td><td>参数值</td><td>是否必须</td><td>实例</td><td>备注</td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td></td><td>香</td><td></td></tr></table></body></html>

### 3.3.5 SS-01-01-03 服务状态查看

### 3.3.5.1 功能描述

通过页面实现查看LLM 运行框架服务的状态。

### 3.3.5.2 处理流程

无

### 3.3.5.3 接口说明

接口 URI：/ai/ops/serve/status  
请求方式：Get  
请求参数：无

返回数据：

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td>Integer</td><td>是</td><td>状态</td></tr></table></body></html>

### 3.3.6 SS-01-01-04 服务版本查看

### 3.3.6.1 功能描述

通过页面实现查看LLM 运行框架服务的版本。

### 3.3.6.2 处理流程

无

### 3.3.6.3 接口说明

接口 URI：/ai/ops/serve/version  
请求方式：Get  
请求参数：无  
返回数据：

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td>ServeVersion</td><td>是</td><td></td></tr><tr><td colspan="4">ServeVersion</td></tr><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>version</td><td>String</td><td>必须</td><td>服务版本</td></tr></table></body></html>

### 3.3.7 SS-01-01-05 服务基本信息查看

### 3.3.7.1 功能描述

通过页面实现查看LLM 运行框架服务的基本信息。

### 3.3.7.2 处理流程

无

### 3.3.7.3 接口说明

接口 URI：/ai/ops/serve/info  
请求方式： Get  
请求参数：无  
返回数据：

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td> timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td>ServeInfo</td><td>是</td><td></td></tr><tr><td colspan="4">ServeInfo</td></tr><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>name</td><td>String</td><td>必须</td><td>服务名称</td></tr><tr><td>host</td><td>String</td><td>必须</td><td>监听IP</td></tr><tr><td>port</td><td>Integer</td><td>必须</td><td>端口</td></tr><tr><td>protocol</td><td>Integer</td><td>必须</td><td>协议</td></tr><tr><td>modelDir</td><td>String</td><td>必须</td><td>模型位置</td></tr></table></body></html>

### 3.3.8 SS-01-01-06 下载接口手册

### ******* 功能描述

页面提供下载手册的功能，可以通过页面下载手册的 pdf 文件。

### ******* 处理流程

![](images/f2564a7cdf81e1b8fd5c8e6fb617e4f7f30fe41bdefbf3a41f26d4e01c7597f1.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示AI一体机管理系统的截图
```


### ******* 接口说明

接口 URI：/ai/ops/serve/download

请求方式：Ge't

请求参数：无

返回数据：

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td></td><td></td><td></td></tr></table></body></html>

### 3.4 模型管理

## 3.4.1 功能点清单

<html><body><table><tr><td>功能点编号</td><td>功能点清单</td><td>优先级</td></tr><tr><td>SS-01-02-01</td><td>查询模型列表</td><td>高</td></tr></table></body></html>

## 3.4.2 重要流程说明

### 3.4.3 SS-01-02-01 查询模型列表

### 3.4.3.1 功能描述

当服务运行时，通过页面可以看到运行的模型有哪些；  
当服务停止时，该列表为空。

### 3.4.3.2 处理流程

<html><body><table><tr><td>模型</td><td>名称</td><td>参数</td><td>格式</td><td>家族</td><td>过期时间</td></tr><tr><td>mistral:latest</td><td>mistral:latest</td><td>7.2B</td><td>gguf</td><td>llama</td><td>2024-10-15 14:50:32</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

查询时，需要先判断一下服务状态，然后再调用服务的API 查询模型列表。

### 3.4.3.3 接口说明

接口 URI：/ai/ops/model/list

请求方式：post

请求参数：

Body：

<html><body><table><tr><td>参数名称</td><td>参数值</td><td>是否必须</td><td>实例</td><td>备注</td></tr></table></body></html>

返回数据：  

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr></table></body></html>

<html><body><table><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td>List<ModelPageVO></td><td>否</td><td></td></tr><tr><td>类名</td><td>属性名</td><td>类型</td><td>备注</td></tr><tr><td rowspan="10">ModelPageVO</td><td>name</td><td>String</td><td>名称</td></tr><tr><td>model</td><td>String</td><td>模型</td></tr><tr><td>size</td><td>String</td><td>模型大小</td></tr><tr><td>digest</td><td>String</td><td>摘要</td></tr><tr><td>format</td><td>String</td><td>格式</td></tr><tr><td>family</td><td>String</td><td>家族</td></tr><tr><td>parameter_size</td><td>String</td><td>参数大小</td></tr><tr><td>quantization_level</td><td>String</td><td>量化等级</td></tr><tr><td>expires_at</td><td>String</td><td>过期时间</td></tr><tr><td>size_vram</td><td>String</td><td>显存大小</td></tr></table></body></html>

### 3.5 SS-01-03 监控管理

## 3.5.1 功能点清单

<html><body><table><tr><td>功能点编号</td><td>功能点清单</td><td>优先级</td></tr><tr><td>SS-01-03-01</td><td>首页</td><td>高</td></tr><tr><td>SS-01-03-02</td><td>显卡详情</td><td>高</td></tr></table></body></html>

## 3.5.2 重要流程说明

### 3.5.3 SS-01-03-01 首页

### 3.5.3.1 功能描述

查看当前一体机的整体监控指标。

### 3.5.3.2 处理流程

![](images/2b3577cc17756b99f1661a90a8cc602d447323e1a4d82f7a158cda010ad44ac7.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示AI一体机管理系统的截图，
```


### 3.5.3.3 接口说明

接口直接调用监控组件。

接口 URI：/swmonitor/manage/v2/panel/data/common/query

请求方式：post

请求参数：

Body：  

<html><body><table><tr><td>参数名称</td><td>参数值</td><td>是否必须</td><td>实例</td><td>备注</td></tr><tr><td>config</td><td>Array</td><td>必须</td><td></td><td></td></tr></table></body></html>

返回数据：  

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td></td><td>香</td><td></td></tr></table></body></html>

### 3.5.4 SS-01-03-02 显卡详情

### 3.5.4.1 功能描述

查看每张显卡的详细数据。

### 3.5.4.2 处理流程

![](images/355bdbc76eb66331bd0fba8e246a718b747c232bfd630459944084d743a51f76.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个显卡监控界面，包含了多个显卡的使用情况和详细信息。
```


### 3.5.4.3 接口说明

接口 URI：/ai/ops/monitor/details  
请求方式：post  
请求参数：  
Body：  
返回数据：

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td></td><td>香</td><td></td></tr></table></body></html>

### 3.6 SS-01-04 安全认证

## 3.6.1 功能点清单

<html><body><table><tr><td>功能点编号</td><td>功能点清单</td><td>优先级</td></tr><tr><td>SS-01-04-01</td><td>Ukey登录</td><td>高</td></tr><tr><td>SS-01-04-02</td><td>用户名口令登录</td><td>高</td></tr><tr><td>SS-01-04-03</td><td>下载口令文件</td><td>高</td></tr></table></body></html>

## 3.6.2 重要流程说明

### 3.6.3 SS-01-04-01 Ukey 登录

### 3.6.3.1 功能描述

用于管理系统的认证，通过插入Ukey 可以登录当前系统。

### 3.6.3.2 处理流程

1、验证证书是否合法时，使用密码卡的指定索引的公钥进行验证；

2、使用证书的公钥验证签名值；

### 3.6.3.3 接口说明

接口 URI：/ai/ops/auth/login/ukey

请求方式：post

请求参数：

Body：  

<html><body><table><tr><td>参数名称</td><td>参数值</td><td>是否必须</td><td>实例</td><td>备注</td></tr><tr><td>userCert</td><td>String</td><td>是</td><td></td><td>用户证书</td></tr><tr><td>signature</td><td>String</td><td>是</td><td></td><td>签名值</td></tr><tr><td>plaintext</td><td>String</td><td>是</td><td></td><td>签名原文</td></tr></table></body></html>

返回数据：

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td>String</td><td>是</td><td>token</td></tr></table></body></html>

### 3.6.4  SS-01-04-02 用户名登录

### 3.6.4.1 功能描述

用于管理系统的认证，通过用户名和密码可以登录当前系统。

### 3.6.4.2 处理流程

1、项目启动时，生成用于登录的用户名和口令（可以随机生成，也可以通过读取指定系统配置文件），并使用指定索引密钥加密存储到文件中；

2、页面提供下载登录配置文件的功能，需要输入加密文件用的密码，后端会将用户名和口令解密，然后写入文件，用传入的口令加密文件返回；

3、用户用口令解密文件之后，即可看到用户名和口令；

4、用户登录时，需要输入用户名和密码，后端完成验证即可登录

### 3.6.4.3 接口说明

接口 URI：/ai/ops/auth/login/file

请求方式：post

请求参数：

Body：  

<html><body><table><tr><td>参数名称</td><td>参数值</td><td>是否必须</td><td>实例</td><td>备注</td></tr><tr><td>username</td><td>String</td><td>是</td><td></td><td>用户名</td></tr><tr><td>password</td><td>String</td><td>是</td><td></td><td>密码</td></tr></table></body></html>

返回数据：  

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td>String</td><td>是</td><td>token</td></tr></table></body></html>

### 3.6.5 SS-01-04-03 下载口令文件

### 3.6.5.1 功能描述

通过点击下载登录配置，输入文件加密口令，即可下载用户名和登录密码的加密文件。

### 3.6.5.2 处理流程

![](images/3b4d5367b9c30eaeb2a3a2541bef8699d8dca51975813f52ebf71c5a9dacd8f1.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个综合密码监管平台的用户界面。界面上方有一个导航栏，包含了多个选项卡，如设备管理中心、运维管理中心、事件中心、可视化中心和系统管理等。当前选中的选项卡是“设备管理中心”，并且在该选项卡下，进一步选择了“设备管理”。

在设备管理页面中，左侧有一个菜单栏，列出了不同的管理类别，如设备资产清单、产品种类、设备类型和设备管理等。右侧是设备管理的具体内容区域，显示了一个设备的基本信息和监控配置。基本信息部分显示了设备的IP地址为************，而监控配置部分则提供了一些操作选项，如下载登录配置和退出登录。

此外，在页面的右上角，有一个“态势大屏”的图标和一个用户名“oper”，表明当前登录的用户是“oper”。整体来看，这个界面设计简洁明了，功能分区清晰，方便用户进行设备管理和监控操作。
```


### ******* 接口说明

接口 URI：/ai/ops/auth/config/download

请求方式：Get

请求参数：

Body：  

<html><body><table><tr><td>参数名称</td><td>参数值</td><td>是否必须</td><td>实例</td><td>备注</td></tr><tr><td>filePassword</td><td>String</td><td>是</td><td></td><td>文件加密口令</td></tr></table></body></html>

返回数据：  

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td>String</td><td>是</td><td></td></tr></table></body></html>

### 3.7 SS-01-05 监控组件

## 3.7.1 功能点清单

<html><body><table><tr><td>功能点编号</td><td>功能点清单</td><td>优先级</td></tr><tr><td>SS-01-05-01</td><td>显卡指标采集</td><td>高</td></tr></table></body></html>

## 3.7.2 重要流程说明

3.7.3 SS-01-05-01

### 3.7.3.1 功能描述

通

### 3.7.3.2 处理流程

需要调研具体实现方式以及需要采集的指标然后实现，有监控组件负责调用即可。  
通过统一的脚本归口获取，需要识别不同类型的显卡  
显存、显存使用情况、算力使用情况、温度、功率、显卡数、显卡型号、研究一下是否能看到哪个进程使用

### 3.7.3.3 接口说明

### 3.8 SS-01-06 字典模块

## 3.8.1 功能点清单

<html><body><table><tr><td>功能点编号</td><td>功能点清单</td><td>优先级</td></tr><tr><td>SS-01-06-01</td><td>查询字典信息</td><td>高</td></tr></table></body></html>

## 3.8.2 重要流程说明

### 3.8.3 SS-01-06-01 查询字典信息

### 3.8.3.1 功能描述

查询字典信息接口，用于页面使用字典的位置。

### 3.8.3.2 处理流程

无

### 3.8.3.3 接口说明

接口 URI：/ai/ops/dict/list  
请求方式：post  
请求参数：

Body：  

<html><body><table><tr><td>参数名称</td><td>参数值</td><td>是否必须</td><td>实例</td><td>备注</td></tr><tr><td>typeCodeList</td><td>String[]</td><td>香</td><td></td><td></td></tr></table></body></html>

返回数据：

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr></table></body></html>

<html><body><table><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td>Map<String,DictVO></td><td>否</td><td></td></tr></table></body></html>

<html><body><table><tr><td>类名</td><td>属性名</td><td>类型</td><td>备注</td></tr><tr><td rowspan="5">DictVO</td><td>typeCode</td><td>String</td><td>字典类型编码</td></tr><tr><td>code</td><td>String</td><td>字典编码</td></tr><tr><td>name</td><td>String</td><td>名称</td></tr><tr><td>remark</td><td></td><td></td></tr><tr><td></td><td>String</td><td>备注</td></tr></table></body></html>

### 3.9 SS-01-07 知识库安全存储

## 3.9.1 功能点清单

<html><body><table><tr><td>功能点编号</td><td>功能点清单</td><td>优先级</td></tr><tr><td>SS-01-07-01</td><td>知识库透明加密</td><td>高</td></tr></table></body></html>

## 3.9.2 重要流程说明

### 3.9.3 SS-01-06-01 知识库透明加密

### 3.9.3.1 功能描述

1、需要在AI 一体机上安装KMS 以及加密插件，并指定加密知识库目录；  
2、只有指定进程或用户可以访问加密目录，其他用户和进程无法访问加密目录。  
3、主密钥使用密码卡

### 3.9.3.2 处理流程

组件信息：  
文件加密插件：V4.1.5  
KMS：V4.1

部署时流程：1、安装文件加密插件

SWXA-CMMI-SD-30

2、加载并启动kms 容器  
3、获取 kms 容器 id  
4、在容器内部执行curl 请求  
4.1、kms 创建密钥  
4.2、kms 添加本地文件加密  
4.3、添加加密目录  
4.4、执行目录加密  
4.5、添加加密目录的权限访问控制

### 3.9.3.3 接口说明

不涉及。

### 3.10 SS-01-08 apikey 管理

## 3.10.1 功能点清单

<html><body><table><tr><td>功能点编号</td><td>功能点清单</td><td>优先级</td></tr><tr><td>SS-01-08-01</td><td>查询 apikey 列表</td><td>高</td></tr><tr><td>SS-01-08-02</td><td>复制apikey</td><td>高</td></tr></table></body></html>

## 3.10.2 重要流程说明

### 3.10.3 SS-01-08-01 查询 apikey 列表

### 3.10.3.1 功能描述

通过页面可以看到当前服务可用的apikey 有哪些，默认显示\*\*\*，可以点击查看看到明文；页面提供复制apikey 的功能，可以复制apikey 的信息。

apikey 一般是 sm-xxxxx 或sk-xxxx，前端显示时做好处理，前三个字符正常显示，后面的显示\*\*\*\*\*

### 3.10.3.2 处理流程

<html><body><table><tr><td></td><td></td></tr><tr><td>API-Key</td><td>操作</td></tr><tr><td>Sm-****☆会</td><td>查看|复制</td></tr><tr><td>Sk-*****☆</td><td>查看|复制</td></tr></table></body></html>

### 3.10.3.3 接口说明

接口 URI：/ai/ops/apikey/list  
请求方式：get  
请求参数：  
返回数据：

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td>List<ApiKeyVO></td><td>香</td><td></td></tr></table></body></html>

<html><body><table><tr><td>类名</td><td>属性名</td><td>类型</td><td>备注</td></tr><tr><td>ApiKeyVO</td><td>apiKey</td><td>String</td><td>apieky</td></tr></table></body></html>

# 第四章 SS-02 智能应用统一门户

总体设计原则：基于统一 web 平台实现用户、权限管理，参考成熟的AI 应用门户设计风格，遵循公司 UI 设计规范，按照低耦合、轻量化、可扩展的原则，完成对智能应用统一门户的设计与开发。

### 4.1 模块划分

<html><body><table><tr><td>功能点编号</td><td>功能点清单</td><td>优先级</td></tr><tr><td>SS-02-01</td><td>用户权限管理</td><td>高</td></tr><tr><td>SS-02-02</td><td>门户管理员管理</td><td>高</td></tr><tr><td>SS-02-03</td><td>门户用户管理</td><td>高</td></tr><tr><td>SS-02-04</td><td>门户管理</td><td>高</td></tr></table></body></html>

## 4.2 重要流程说明

### 4.3 SS-02-01 用户权限管理-基础网关

### 4.3.1 功能点清单

<html><body><table><tr><td>功能点编号</td><td>功能点清单</td><td>优先级</td></tr><tr><td>SS-02-01-01</td><td>用户身份和用户类型</td><td>高</td></tr><tr><td>SS-02-01-02</td><td>门户管理服务API代理</td><td>关闭</td></tr><tr><td>SS-02-01-03</td><td>门户访问代理和鉴权</td><td>高</td></tr><tr><td>SS-02-01-04</td><td>应用访问代理和鉴权</td><td>高</td></tr><tr><td>SS-02-01-05</td><td>动态更新路由</td><td>高</td></tr></table></body></html>

### 4.3.2 重要流程说明

![](images/04e33c5f706426003896ccf158fe58b8124f7bc035f8cc5a4ea25131978e3d28.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个系统架构图，描述了不同角色（运维人员、系统管理员、门户管理员和普通用户）在系统中的操作流程和交互方式。以下是详细描述：

## 角色与操作
1. **运维人员**
   - **1-1 登录**：运维人员登录系统。
   - **1-2 启动**：启动运行框架。
   - **1-3 后台执行**：负责管理运行框架的启动、监控硬件的使用情况、后续添加模型的管理。

2. **系统管理员**
   - **2-1 登录**：系统管理员登录系统。
   - **2-2 创建门户管理员**：创建门户管理员并绑定门户管理员与Dify实例。
   - **2-3 鉴权、请求转发**：进行鉴权和请求转发。
   - **2-4 启动**：启动相关组件。
   - **2-5 添加供应商**：添加供应商。

3. **门户管理员**
   - **3-1 登录**：门户管理员登录系统。
   - **3-2 创建普通用户**：创建普通用户。
   - **3-3 登录dify实例，在dify实例内操作**：登录Dify实例并在其中进行操作。
   - **3-4 访问具体实例**：访问具体实例的应用。

4. **普通用户**
   - **4-1 登录**：普通用户登录系统。
   - **4-2 统一门户页面**：访问统一门户页面。
   - **4-3 api调用**：进行API调用。
   - **4-4 接口调用**：进行接口调用。
   - **4-5 大模型运算**：进行大模型运算。

## 系统组件与流程
- **Nginx**：作为反向代理服务器，处理用户的请求。
- **一体机管理系统进程**：负责启动运行框架、监控硬件情况。
- **统一web平台**：进行鉴权、转发、管理门户管理员。
- **智能应用管理&统一门户**：管理智能应用和统一门户。
- **Dify实例1**：具体的Dify实例，用于处理业务逻辑。
- **基础网关-请求转发**：进行请求转发。
- **secuLlama/VLLM管理组件（容器化部署）**：管理模型的容器化部署。
- **Deepseek系列模型、Qwen系列模型、Llama系列模型**：各种大模型，用于进行模型运算。

## 流程说明
- 运维人员通过登录系统，启动运行框架，并进行后台执行操作。
- 系统管理员登录后，创建门户管理员并进行相关配置和操作。
- 门户管理员登录后，创建普通用户并进行相关操作。
- 普通用户登录后，通过统一门户页面进行各种操作，包括访问具体实例、进行API调用和接口调用等。
- 各种请求通过Nginx进行转发，经过统一web平台的鉴权和转发，最终到达智能应用管理和Dify实例进行处理。
- 大模型运算通过secuLlama/VLLM管理组件进行管理，并使用Deepseek系列模型、Qwen系列模型和Llama系列模型进行运算。

这个架构图清晰地展示了各个角色的操作流程和系统组件之间的交互关系。
```


### 4.3.3 用户身份和用户类型

### 4.3.3.1 功能描述

![](images/d746ba64b5dd8b784629eb456792a9bbd8937492c4c9a8fec0442fcab6e3a31e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个系统架构图，描述了不同角色之间的关系以及他们如何与系统中的各个模块进行交互。以下是详细描述：

1. **角色分类**：
   - **super角色（超级管理员）**：位于最顶层，拥有最高权限。
   - **oper角色（操作员）**：分为两个子角色，分别是“门户管理员-2”和“门户管理员-1”。
   - **user角色（用户）**：分为三个子角色，分别是“门户用户1-1”、“门户用户1-2”和“门户用户1-3”。

2. **系统管理员**：
   - 超级管理员直接管理系统管理员。

3. **门户管理**：
   - 系统管理员管理多个门户（门户1、门户2、门户N）。
   - 每个门户由一个或多个门户管理员负责管理。

4. **应用市场**：
   - 应用市场包含多个应用模板（应用模板1、应用模板2、应用模板N）。

5. **Dify实例**：
   - 每个门户对应一个或多个Dify实例（Dify实例-1、Dify实例-2、Dify实例-N）。
   - Dify实例中包含多个应用（应用1、应用2、应用3）。

6. **SecuLLama**：
   - 所有的Dify实例最终都指向SecuLLama模块。

## 交互流程：
- 超级管理员管理系统管理员。
- 系统管理员管理门户管理员和门户。
- 门户管理员管理门户用户和Dify实例。
- 门户用户通过Dify实例访问应用。
- 所有应用最终都通过SecuLLama模块进行处理。

这个架构图展示了从超级管理员到普通用户的层级关系，以及他们如何通过不同的门户和Dify实例访问和使用应用。
```


用户身份即用户登录门户后系统为用户分配的token，基于统一web 登录获取用户类型即角色划分，分为三类：

系统管理员：super 角色，负责Dify 实例管理、门户管理、应用模板管理$\bullet$ 门户管理员：oper 角色，一个门户至少有一个门户管理员，负责应用管理、门户用户管理、Dify 控制台管理$\bullet$ 门户用户：普通用户，可以访问某个门户，使用门户下的应用

各角色权限如下：  

<html><body><table><tr><td rowspan="2">一级权限</td><td rowspan="2">二级权限</td><td>super</td><td>oper</td><td>audit</td><td>user</td></tr><tr><td>系统管理员</td><td>门户管理员</td><td>预留</td><td>门户用户</td></tr><tr><td rowspan="3">基础功能</td><td>登录、退出</td><td>√</td><td>√</td><td></td><td>√</td></tr><tr><td>个人信息维护</td><td>√</td><td>√</td><td></td><td>√</td></tr><tr><td>门户创建</td><td>√</td><td></td><td></td><td></td></tr><tr><td rowspan="4">门户管理</td><td>门户编辑</td><td>√</td><td></td><td></td><td></td></tr><tr><td>门户删除</td><td>√</td><td></td><td></td><td></td></tr><tr><td>门户启停</td><td>√</td><td></td><td></td><td></td></tr><tr><td>创建</td><td>√</td><td></td><td></td><td></td></tr><tr><td rowspan="3">门户管理员 管理</td><td>编辑</td><td></td><td></td><td></td><td></td></tr><tr><td>删除</td><td>√</td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td rowspan="4">门户用户管 理</td><td>创建</td><td></td><td>√</td><td></td><td></td></tr><tr><td>编辑</td><td></td><td>√</td><td></td><td>√</td></tr><tr><td>删除</td><td></td><td>√</td><td></td><td></td></tr><tr><td>绑定/解绑</td><td></td><td>√</td><td></td><td></td></tr><tr><td rowspan="4">门户访问 应用访问 Dify 管理端</td><td>首页对话</td><td></td><td></td><td></td><td>√</td></tr><tr><td>应用探索</td><td></td><td></td><td></td><td>√</td></tr><tr><td>应用对话</td><td></td><td></td><td></td><td>√</td></tr><tr><td>Dify管理端</td><td></td><td>√</td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

注：系统管理员在创建门户时会同时创建门户管理员；在删除门户时会删除对应的门户管理员

### 4.3.3.2 处理流程

![](images/6d858ecb32c79900253b1d9323a4a4da49033241ec1cb69a2a6cc8ea0c51e2b6.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个基于统一web实现的系统角色结构图。图中分为三个主要角色层次：super角色、oper角色和user角色。

1. **super角色**：
   - 在最顶层，有一个“系统管理员”角色，属于super角色。这个角色拥有最高的权限，负责整个系统的管理和维护。

2. **oper角色**：
   - 在中间层，有两个“门户管理员”角色，分别是“门户管理员-1”和“门户管理员-2”，属于oper角色。这两个角色由系统管理员管理，负责各自门户的管理和操作。

3. **user角色**：
   - 在最底层，有三个“门户用户”角色，分别是“门户用户1-1”、“门户用户1-2”和“门户用户1-3”，属于user角色。这些用户由对应的门户管理员管理，负责具体的操作和使用。

此外，图片还标注了“独立实现”的部分，表示门户用户是由门户管理员独立管理的，与系统管理员直接管理的门户管理员不同。

整体来看，这张图清晰地展示了系统管理员、门户管理员和门户用户之间的层级关系和管理结构。
```


角色、系统管理员、门户管理员基于统一 web 实现，便于后期权限扩展和账号维护；  
门户用户在本系统中独立实现，便于适配、对接客户公司账号体系，定制需求。

### 4.3.3.3 接口说明

### 4.3.4 门户管理服务 API 代理--关闭

### 4.3.4.1 功能描述

当系统管理员/门户管理员登录统一智能门户查看、管理与 dify 实例无关的内容时，基础网关需要负责将已经通过统一 web 平台鉴权后的请求转发到智能门户进行业务处理；此过程不涉及鉴权，仅作转发；且此转发路由为固定路由，不需要动态添加和删除。

场景说明：系统管理员、门户管理员、门户用户访问不需要 dify 的页面时。

### 4.3.4.2 处理流程

![](images/28e90d3318327c7b66981d864953e6f0d9c75e575636fd30e08df0a189cd917c.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个系统管理员或门户管理员与智能门户之间的请求处理流程。流程分为七个步骤，具体如下：

1. **基础请求**：系统管理员或门户管理员发起一个基础请求。
2. **请求转发**：请求首先被转发到nginx服务器。
3. **token鉴权**：nginx将请求转发到统一web进行token鉴权。
4. **请求转发**：鉴权通过后，请求被转发到基础网关。
5. **请求转发**：基础网关再次将请求转发到智能门户。
6. **请求处理**：智能门户对请求进行处理。
7. **返回请求**：处理完成后，智能门户将结果返回给系统管理员或门户管理员。

整个流程展示了从请求发起、鉴权、处理到结果返回的完整过程，涉及nginx、统一web、基础网关和智能门户等多个组件。
```


### 4.3.5 门户访问代理

### 4.3.5.1 功能描述

当系统管理员/门户管理员需要通过智能门户访问具体的 dify 实例时，会跳转至具体的dify 实例登录页，后续所有的操作都是通过基础网关进行转发的。此时需要通过基础网关把请求转发具体dify 实例获取具体信息或完成具体业务。

场景说明：

1、门户管理员在dify 实例上创建应用时

### 4.3.5.2 处理流程

### 门户访问Dify内容转发

![](images/4f8b2f1d473632f701c78ed94b32f6840eebf28eed012146f93a25edb9b46769.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个系统架构图，描述了从门户管理员到Dify实例的数据请求流程。具体步骤如下：

1. **基础请求**：门户管理员发起一个基础请求，这个请求首先被发送到nginx服务器。
2. **请求转发**：nginx服务器接收到请求后，将其转发给基础网关。
3. **请求dify**：基础网关接收到请求后，再将其转发给Dify实例进行处理。
4. **返回请求**：Dify实例处理完请求后，将结果返回给基础网关，基础网关再将结果返回给nginx服务器，最后由nginx服务器将结果返回给门户管理员。

整个流程通过箭头和数字标注清晰地展示了数据的流动方向和顺序。
```


### 4.3.6 应用访问代理

### 4.3.6.1 功能描述

门户用户登录智能门户后，会看到可以使用的应用，当访问具体应用时，此时基础网关会将请求直接转发至对应的dify 实例，此时请求不经过智能门户。

场景说明：门户用户访问具体应用时。

### 4.3.6.2 处理流程

![](images/0ec5da458a85d5dbd6fc2832eaded758829d9698015be31e2400eb17acdea1dd.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个系统架构图，描述了从门户用户发起请求到最终返回响应的整个流程。具体步骤如下：

1. 门户用户发起一个基础请求。
2. 请求首先到达nginx服务器。
3. nginx将请求转发给基础网关。
4. 基础网关进行路由校验。
5. 如果路由匹配，则继续处理；如果路由不匹配，则查询数据库。
6. 查询数据库后，返回查询结果。
7. 根据查询结果，如果路由存在，则继续处理；如果路由不存在，则报错。
8. 最终，响应返回给门户用户。

在这个过程中，涉及到的主要组件包括门户用户、nginx服务器、基础网关、数据库和Dify实例。每个组件在流程中扮演着不同的角色，共同完成请求的处理和响应。
```
  
门户访问应用代理转发

### 4.3.7 动态更新路由

### 4.3.7.1 功能描述

由于 dify 实例是可以动态添加的，因此基础网关的路由需要支持动态更新；为了降低基础网关与门户系统之间的耦合，由基础网关查询数据库进行路由的更新。

场景说明：当系统管理员创建新的dify 实例后，第一笔访问 dify 应用的请求。

### 4.3.7.2 处理流程

参考4.3.6 应用访问代理章节即可。

### 4.4 SS-02-02 门户管理员管理

### 4.4.1 功能点清单

<html><body><table><tr><td>功能点编号</td><td>功能点清单</td><td>优先级</td></tr><tr><td>SS-02-02-01</td><td>新增门户管理员</td><td>高</td></tr><tr><td>SS-02-02-02</td><td>编辑门户管理员</td><td>关闭</td></tr><tr><td>SS-02-02-03</td><td>删除门户管理员</td><td>高</td></tr><tr><td>SS-02-02-04</td><td>门户管理员列表</td><td>高</td></tr><tr><td>SS-02-02-05</td><td>门户管理员登录</td><td>关闭</td></tr><tr><td>SS-02-02-06</td><td>门户管理员退出</td><td>关闭</td></tr><tr><td>SS-02-02-07</td><td>查询门户管理员信息</td><td>高</td></tr></table></body></html>

### 4.4.2 重要流程说明

门户管理员对应统一 web 平台中的 oper 角色用户，用户的创建、修改、删除、查询以及登录和登出使用统一web 平台功能即可。当智能门户系统需要访问具体的接口时，比如为dify 实例创建门户管理员时，可以通过统一 web 平台的 sdk 或者 RPC 接口调用等方式实现。

说明：需要取消门户管理员进行密码重置的功能、取消系统管理员重置门户管理员密码的功能、以及较长时间时候系统管理员需要重置密码的要求。

### 4.4.3 新增门户管理员-内部接口调用

### 4.4.3.1 功能描述

当创建门户(dify 实例)时，会要求输入门户管理员名称和门户管理员登录密码，此时需要在统一 web 平台创建对应的 oper 用户，并在智能门户管理系统存储门户与门户管理员的关联信息。因此，该功能一方面调用统一 web 平台生成用户，另一方面存储门户与管理员的关联信息。

使用场景说明：创建门户时。

### 4.4.3.2 处理流

![](images/375b5d90d2ac05c1e3ffd26fd120812533170b2eca32870788bdc629361116ae.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张流程图，描述了智能门户和统一web之间的用户创建和管理流程。流程从智能门户开始，经过门户创建、参数校验等步骤，最终返回用户id。在参数校验环节，如果用户名称、用户口令、门户id校验不通过，则会返回error；如果通过，则会判断是否创建新用户，如果不创建用户，则直接存储关联关系并返回用户id；如果创建新用户，则会进一步判断用户创建是否成功，如果失败则返回error，如果成功则会重置用户口令，并存储关联关系，最后返回用户id。
```


### 4.4.3.3 接口说明

接口 URI：/\${统一路径}/portal/manager/add请求方式：POST

请求参数：  

<html><body><table><tr><td>参数名称</td><td>参数值</td><td>是否必须</td><td>实例</td><td>备注</td></tr><tr><td>userName</td><td>String</td><td>是</td><td></td><td>用户名称，支持3-15，只 能包含大、小写字母、数 字、特殊字符_- 正则：[w-]{3,15}$</td></tr><tr><td>userPwd</td><td>String</td><td>是</td><td></td><td>用户密码，支持长度 8-64，至少包含大写字 母、小写字母、数字、特 殊字符 ~!@#$%^&*{};,?/ 的 三</td></tr><tr><td>portalld</td><td>Long</td><td>是</td><td></td><td>种 门户id</td></tr><tr><td>addNewUser</td><td>Boolean</td><td>是</td><td></td><td>是否创建新用户</td></tr><tr><td>userId</td><td>String</td><td>香</td><td></td><td>当addNewUser 为 true 时 必填</td></tr></table></body></html>

返回数据：  

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td>Long</td><td>香</td><td>门户管理员id</td></tr></table></body></html>

### 4.4.4 删除门户管理员-内部接口调用

### 4.4.4.1 功能描述

当删除门户(dify 实例)时，系统管理员可以选择是否删除用户，如果选择否，则只删除关联关系，不需要调用统一web 平台删除用户；如果选择时，则需要调用。

使用场景说明：删除门户。lo

### 4.4.4.2 处理流程

![](images/471cef91965da68570098e32b0aa7b07eb091c23d09d903fb4b36d7a82899484.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张智能门户的流程图，描述了从门户删除到返回门户id的整个过程。首先，流程从一个黑色圆点开始，表示流程的起点。接着是“门户删除”步骤，然后进行“参数校验”。之后，查询与门户关联的管理员，并对门户id和是否删除用户进行校验。如果校验不通过，则进入“error”状态并终止流程；如果校验通过，则根据是否删除用户的选项进行不同的处理：如果不删除用户，则直接进入“不删除”状态；如果选择删除用户，则进一步判断用户删除是否成功，如果失败则进入“error”状态，如果成功则继续流程。最后，删除关联关系并返回门户id，流程结束于另一个黑色圆点。在流程图的右侧，有一个“统一web”的标签，可能表示这个流程是在统一的web环境下进行的。
```


### 4.4.4.3 接口说明

接口 URI：/\${统一路径}/portal/manager/delete请求方式：POST

请求参数：

<html><body><table><tr><td>参数名称</td><td>参数值</td><td>是否必须</td><td>实例</td><td>备注</td></tr><tr><td>portalId</td><td>Long</td><td>是</td><td></td><td>门户id</td></tr><tr><td>deleteUser</td><td>Boolean</td><td>是</td><td>True</td><td>是否删除管理员</td></tr></table></body></html>

返回数据：  

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td>Long</td><td>香</td><td>门户id</td></tr></table></body></html>

### 4.4.5 门户管理员列表

### 4.4.5.1 功能描述

查询门户管理员列表。

### 4.4.5.2 处理流

逻辑简单，不做赘述

### 4.4.5.3 接口说明

接口 URI：/\${统一路径}/portal/manager/list

请求方式：post

请求参数：

<html><body><table><tr><td>参数名称</td><td>参数值</td><td>是否必须</td><td>实例</td><td>备注</td></tr><tr><td>userName</td><td>String</td><td>否</td><td>HSM</td><td>用户名称</td></tr><tr><td>portalId</td><td>Long</td><td>否</td><td></td><td>门户id</td></tr><tr><td>bindStatus</td><td>boolean</td><td>香</td><td>false</td><td>是否绑定门户</td></tr></table></body></html>

返回数据：  

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td>List<PortalManagerVO></td><td></td><td>门户管理员信息</td></tr></table></body></html>

<html><body><table><tr><td>类名</td><td>属性名</td><td>类型</td><td>备注</td></tr><tr><td rowspan="3">PortalManagerVO</td><td>id</td><td>Long</td><td>用户id</td></tr><tr><td>userName</td><td>String</td><td>用户名称</td></tr><tr><td>portalId</td><td>Long</td><td>门户id</td></tr></table></body></html>

### 4.4.6 查询门户管理员信息

### 4.4.6.1 功能描述

根据门户管理员 id 查询门户管理员信息，重点显示对应的门户信息，用于创建门户用户时，回显对应的门户。

### 4.4.6.2 处理流

逻辑简单，不做赘述

### 4.4.6.3 接口说明

接口 URI：/\${统一路径}/portal/manager/get

请求方式：get

请求参数：

userId: 用户 id

返回数据：  

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td>PortalManagerVO</td><td></td><td>门户管理员信息</td></tr></table></body></html>

<html><body><table><tr><td>类名</td><td>属性名</td><td>类型</td><td>备注</td></tr><tr><td rowspan="3">PortalManagerVO</td><td>id</td><td>Long</td><td>用户id</td></tr><tr><td>userName</td><td>String</td><td>用户名称</td></tr><tr><td>portalId</td><td>Long</td><td>门户id</td></tr></table></body></html>

### 4.5 SS-02-03 门户用户管理

### 4.5.1 功能点清单

<html><body><table><tr><td>功能点编号</td><td>功能点清单</td><td>优先级</td></tr><tr><td>SS-02-03-01</td><td>新增门户用户</td><td>高</td></tr><tr><td>SS-02-03-02</td><td>编辑门户用户</td><td>高</td></tr><tr><td>SS-02-03-03</td><td>删除门户用户</td><td>高</td></tr><tr><td>SS-02-03-04</td><td>门户用户列表</td><td>高</td></tr><tr><td>SS-02-03-05</td><td>门户用户登录</td><td>高</td></tr><tr><td>SS-02-03-06</td><td>门户用户退出</td><td>高</td></tr><tr><td>SS-02-03-08</td><td>绑定门户</td><td>高</td></tr><tr><td>SS-02-03-09</td><td>解绑门</td><td>高</td></tr><tr><td>SS-02-04-10</td><td>获取用户详情</td><td>高</td></tr></table></body></html>

### 4.5.2 重要流程说明

门户用户可以理解为业务用户，是真正使用 AI 应用的用户，这类用户通过登录系统，可以看到有哪些应用，并且可以访问具体的应用进行对话或其他操作。为了能够保证认证逻辑的统一，使用统一web 平台进行 token 验证，因此需要在统一 web 平台预置该角色（门户用户的父角色为门户管理员，即oper 用户；这样可以保证系统管理员（super 用户）无法删除这些门户用户。），角色名称为：portal_user。此外，此类用户的增删改查需要单独开发页面使用，由智能门户系统作为入口，调用统一 web 平台创建用户，智能门户会存储其他信息：用户id、用户名、用户与门户的关联关系。

![](images/7b559282f2160619e127a56f98b707e7926d683e8da57bbc480910caa6650359.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示用户管理界面的图片。界面上有用户名、门户和绑定状态的输入框，以及查询和重置按钮。下方有一个表格，列出了两个用户的详细信息：用户名、门户、最后登录时间和操作选项。具体信息如下：

1. 用户名：user01，门户为空，最后登录时间为2024-10-15 14:50:32，操作选项包括重置口令和更多（绑定、解绑、删除）。
2. 用户名：user02，门户为portal01，最后登录时间为2024-10-13 12:00:00，操作选项同样包括重置口令和更多（绑定、解绑、删除）。

此外，界面上还有新增、绑定和解绑按钮，用于对用户进行相应的操作。
```


### 4.5.3 新增门户用户

### 4.5.3.1 功能描述

门户管理员登录之后，可以在门户用户管理页面，新增门户用户。新增的用户可以登录智能门户访问该门户管理员绑定的dify 实例内的应用。

场景说明：门户管理员、dify 实例、应用已经存在，如果需要为某个部门或团队创建访问应用的账号时，需要新增门户用户。

### 4.5.3.2 处理流程

![](images/c28138a02663e40340a9760bb75016f61ae9d453d886c9f9835d1e98f92b3ef0.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了智能门户系统中新增门户用户的过程，以及一个新增用户的表单界面。

## 新增门户用户流程图

1. **基础请求**：门户管理员发起一个基础请求。
2. **请求转发**：请求被转发到nginx服务器。
3. **token鉴权**：nginx服务器将请求转发到统一web进行token鉴权。
4. **请求转发**：鉴权通过后，请求再次被转发到基础网关。
5. **请求转发**：基础网关将请求转发到智能门户。
6. **内部逻辑处理、添加用户**：智能门户进行内部逻辑处理，并添加用户信息。
7. **存储其他信息**：智能门户存储其他相关信息。
8. **返回请求**：处理完成后，智能门户将结果返回给门户管理员。

## 新增用户表单界面

- **用户名**：输入框用于输入新用户的用户名。
- **口令**：输入框用于输入新用户的密码。
- **确认口令**：输入框用于确认新用户的密码。
- **门户**：显示当前登录的门户管理员对应的门户信息。
- **取消**：按钮用于取消操作。
- **提交**：按钮用于提交新增用户的信息。

这个流程图和表单界面共同展示了在智能门户系统中如何新增一个门户用户，包括请求的流转过程和用户信息的输入。
```


### 4.5.3.3 接口说明

接口 URI：/\${统一路径}/portal/user/add  
请求方式：post  
请求参数：

Body：  

<html><body><table><tr><td>参数名称</td><td>参数值</td><td>是否必须</td><td>实例</td><td>备注</td></tr><tr><td>userName</td><td>String</td><td>是</td><td></td><td>用户名称，支持3-15，只 能包含大、小写字母、数 字、特殊字符_- 正则：^[w-]{3,15}$</td></tr><tr><td>userPwd</td><td>String</td><td>是</td><td></td><td>用户密码，支持长度 8-64，至少包含大写字 母、小写字母、数字、特 殊字符 ~!@#$%^&*{};,?/ 的 三</td></tr><tr><td>portalld</td><td>Long</td><td>是</td><td></td><td>种 门户id</td></tr></table></body></html>

返回数据：  

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td>Long</td><td></td><td>用户id</td></tr></table></body></html>

### 4.5.4 编辑门户用户

### 4.5.4.1 功能描述

用于实现对门户用户的口令重置的功能。

场景说明：只有门户管理员可以在该页面下修改用户口令；系统管理员在系统管理的用户管理那可以修改口令；门户用户登录后，可以在个人中心修改。

### 4.5.4.2 处理流程

![](images/8a4eaa2c814a46d7799133eda2dff563544f5870c4ac028de4feffec34278522.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张流程图，描述了一个用户身份验证和口令修改的过程。流程从顶部开始，首先进行参数校验，然后检查用户ID和用户口令是否通过校验。如果通过校验，会进一步检查用户是否存在。如果用户存在，则进行口令修改，并返回响应；如果用户不存在，则返回错误信息。如果在用户ID和用户口令校验阶段未通过，则直接返回错误信息。整个流程的终点是返回响应或错误信息后的结束状态。
```


![](images/0be1aee237f9e033751007d3f720e42d6af4355f04e66c523eb26d07ae18777c.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示重置口令界面的图片。界面上方有一个标题“重置口令”，右上角有一个关闭按钮（X）。界面中间有三个输入框，分别是“用户名”、“口令”和“确认口令”。用户名输入框中已经填写了“user01”，而口令和确认口令输入框中则显示“请输入”。在界面底部有两个按钮，一个是灰色的“取消”按钮，另一个是蓝色的“提交”按钮。
```


### 4.5.4.3 接口说明

接口 URI：/\${统一路径}/portal/user/modify  
请求方式：post  
请求参数：

Body：  

<html><body><table><tr><td>参数名称</td><td>参数值</td><td>是否必须</td><td>实例</td><td>备注</td></tr><tr><td>userId</td><td>Long</td><td>是</td><td></td><td>用户id</td></tr><tr><td>userPwd</td><td>String</td><td>是</td><td></td><td>用户密码，支持长度 8-64，至少包含大写字 母、小写字母、数字、特 殊字 符 ~!@#$%^&*{};?/ 的 三 种</td></tr></table></body></html>

返回数据：  

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td>Long</td><td></td><td>用户id</td></tr></table></body></html>

### 4.5.5 删除门户用户

### 4.5.5.1 功能描述

当门户管理员需要删除某个门户用户时，可以使用该功能。  
场景说明：只有门户管理员可以在该页面删除门户用户。

### 4.5.5.2 处理流程

![](images/4b0c16bb24ab7804f645dbbf95f3d211b220022025d67cf5b4f461bc816f9659.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张流程图，描述了一个用户信息删除的处理流程。流程从顶部开始，首先进行参数校验，如果用户ID通过校验，则进一步检查用户是否存在。如果用户存在，则执行删除用户信息的操作，并返回响应；如果用户不存在，则返回错误信息。如果在参数校验阶段用户ID不通过，则直接返回错误信息。整个流程在处理完后结束。
```


### 4.5.5.3 接口说明

接口 URI：/\${统一路径}/portal/user/delete

请求方式：post

请求参数：

Body：  

<html><body><table><tr><td>参数名称</td><td>参数值</td><td>是否必须</td><td>实例</td><td>备注</td></tr><tr><td>userIds</td><td>List<Long></td><td>是</td><td></td><td>用户id列表</td></tr></table></body></html>

返回数据：

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr></table></body></html>

<html><body><table><tr><td>result</td><td>void</td><td></td><td></td></tr></table></body></html>

### 4.5.6 门户用户列表

### 4.5.6.1 功能描述

用户分页查询门户用户的列表信息，门户管理员智能看到绑定当前门户管理员所属门户的用户和未绑定任何门户的用户，取消根据门户选择，当前页面只有门户管理员可以访问，因此能看到的数据是门户管理员绑定门户下的用户以及未绑定门户的用户，因此不存在选择门户的可能，只需要通过选择绑定状态即可区分。

### 4.5.6.2 处理流程

![](images/38c8ac7b056a2e98abf37b675efa7b08ad29e31c2e86c82a138e11e697009610.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个用户管理界面，包含用户名、门户和绑定状态的筛选选项，以及查询和重置按钮。下方有新增、绑定和解绑的操作按钮。表格列出了两个用户的详细信息，包括用户名、门户、最后登录时间和操作选项，如重置口令、更多、绑定、解绑和删除。
```


### 4.5.6.3 接口说明

接口 URI：/\${统一路径}/portal/user/page

请求方式：post

请求参数：

Body：  

<html><body><table><tr><td>参数名称</td><td>参数值</td><td>是否必须</td><td>实例</td><td>备注</td></tr><tr><td>userName</td><td>String</td><td>否</td><td>HSM</td><td>用户名称</td></tr><tr><td>bindStatus</td><td>boolean</td><td>否</td><td>false</td><td>是否绑定门户</td></tr><tr><td>pageNum</td><td>Integer</td><td>是</td><td>1</td><td>页码</td></tr></table></body></html>

返回数据：  

<html><body><table><tr><td>pageSize</td><td>Integer</td><td>是</td><td>10</td><td>页面大小</td></tr></table></body></html>

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td>object</td><td>是</td><td>响应</td></tr></table></body></html>

<html><body><table><tr><td>类名</td><td>属性名</td><td>类型</td><td>备注</td></tr><tr><td rowspan="4">PageDTO <PortalUserPageVO></td><td>pageNum</td><td>Integer</td><td>页码</td></tr><tr><td>pageSize</td><td>Integer</td><td>页面大小</td></tr><tr><td>total</td><td>long</td><td>总数</td></tr><tr><td>List</td><td>List <PortalUserVO></td><td>产品列表</td></tr><tr><td rowspan="5">PortalUserVO</td><td>id</td><td>Long</td><td>用户id</td></tr><tr><td>userName</td><td>String</td><td>用户名</td></tr><tr><td>portalld</td><td>Long</td><td>门户id</td></tr><tr><td>portalName</td><td>String</td><td>门户名称</td></tr><tr><td>loginDate</td><td>Date</td><td>最后登录时间</td></tr></table></body></html>

### 4.5.7 登录

### 4.5.7.1 功能描述

提供用户登录验证功能，通过验证码提供防刷限制，提供口令输错 5 次锁定用户防暴力破解。

### 4.5.7.2 处理流程

![](images/7901f100394ef65e96f4f06da85b7036e3345fe0d1eccc1fa67997d79adf829d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张用户登录流程图，详细描述了用户登录时的验证步骤和可能的结果。流程从用户登录开始，用户需要输入用户名、手机号、邮箱以及口令（图片二维码）。接下来，系统会验证图片验证码是否正确，如果错误则提示验证码错误；如果正确，则进一步检查用户是否被锁定，如果用户被锁定则提示用户锁定；如果未被锁定，则校验用户是否存在，如果不存在则提示用户不存在；如果存在，则校验用户口令是否正确，如果错误则登录失败，并检查用户锁定次数是否超过5次，如果超过则锁定用户，如果未超过则失败次数加1；如果口令正确，则登录成功。
```


### 4.5.7.3 接口说明

<html><body><table><tr><td>接口详情</td><td colspan="4">用户登录</td></tr><tr><td>接口地址</td><td colspan="4">/${统一路径}/portal/user/login</td></tr><tr><td>调用方式</td><td colspan="4">POST方式</td></tr><tr><td>功能描述</td><td colspan="4">用户登录</td></tr><tr><td></td><td>参数名</td><td>类型</td><td>是否必填</td><td>说明</td></tr><tr><td rowspan="4">输入参数</td><td>userName</td><td>String</td><td>否</td><td>手机号（手机号、邮箱、 用户名、不能同时为空)</td></tr><tr><td>userPwd</td><td>String</td><td>香</td><td>邮箱（手机号、邮箱、用 户名、不能同时为空)</td></tr><tr><td>captchaId</td><td>String</td><td>是</td><td>图片验证码id</td></tr><tr><td>captcha</td><td>String</td><td>是</td><td>图片验证码</td></tr><tr><td rowspan="4">输出参数</td><td> status</td><td>string</td><td>非必须</td><td>0 成功，其他失败</td></tr><tr><td>message</td><td>string</td><td>非必须</td><td></td></tr><tr><td>result</td><td>object</td><td>非必须</td><td></td></tr><tr><td>Ttoken</td><td>String</td><td>非必须</td><td></td></tr></table></body></html>

<html><body><table><tr><td>接口详情</td><td colspan="4">生成图片验证码</td></tr><tr><td>接口地址</td><td colspan="4">/${统一路径}/portal/user/captcha/image/generate</td></tr><tr><td>调用方式</td><td colspan="4">POST</td></tr><tr><td>功能描述</td><td colspan="4">生成图片验证码</td></tr><tr><td></td><td>参数名</td><td>类型</td><td>是否必填</td><td>说明</td></tr><tr><td rowspan="3">输出参数</td><td>status</td><td>string</td><td>非必须</td><td>0 成功，其他失败</td></tr><tr><td>message</td><td>string</td><td>非必须</td><td></td></tr><tr><td>result</td><td>object</td><td>必须</td><td></td></tr><tr><td></td><td>卜 captchaId</td><td>String</td><td>香</td><td>图片验证码Id</td></tr><tr><td></td><td>|- captchaBase64</td><td>String</td><td>是</td><td>图片验证码的base64字 符串</td></tr></table></body></html>

<html><body><table><tr><td>接口详情</td><td>获取验证码图片</td></tr><tr><td>接口地址</td><td>/${统一路径}/portal/user/captcha/image/get</td></tr></table></body></html>

<html><body><table><tr><td>调用方式</td><td colspan="3">POST</td></tr><tr><td>功能描述</td><td colspan="3">获取图片验证码</td></tr><tr><td></td><td>参数名</td><td>类型</td><td>是否必填</td><td>说明</td></tr><tr><td>输入参数</td><td>captchald</td><td>String</td><td>是</td><td>验证码id</td></tr><tr><td rowspan="3">输出参数</td><td>status</td><td>string</td><td>非必须</td><td>0 成功，其他失败</td></tr><tr><td>message</td><td>string</td><td>非必须</td><td></td></tr><tr><td>result</td><td>void</td><td>香</td><td></td></tr></table></body></html>

<html><body><table><tr><td>接口详情</td><td colspan="3">获取用户状态，判断是否需要获取验证吗</td></tr><tr><td>接口地址</td><td colspan="3">/${统一路径}/portal/user/getUserStatus</td></tr><tr><td>调用方式</td><td colspan="3">Get</td></tr><tr><td>功能描述</td><td colspan="3">获取用户状态</td></tr><tr><td></td><td>参数名</td><td>类型</td><td>是否必填</td><td>说明</td></tr><tr><td>输入参数</td><td>userName</td><td>String</td><td>是</td><td>用户名称</td></tr><tr><td rowspan="4">输出参数</td><td> status</td><td>string</td><td>非必须</td><td>0成功，其他失败</td></tr><tr><td>message</td><td>string</td><td>非必须</td><td></td></tr><tr><td>result</td><td>UserStatusVO</td><td>必须</td><td>用户状态信息</td></tr><tr><td>|-codeDisplay</td><td> Integer</td><td>必须</td><td>0：不需要 1：需要</td></tr></table></body></html>

### 4.5.8 登出

### 4.5.8.1 功能描述

当前门户用户退出登录，后台删除对应 token。

### 4.5.8.2 处理流程

### 4.5.8.3 接口说明

接口 URI：/\${统一路径}/portal/user/logout

请求方式：Get

请求参数：

返回数据：

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td></td><td></td><td></td></tr></table></body></html>

### 4.5.9 绑定门户

### 4.5.9.1 功能描述

支持批量（一个或多个）为用户绑定门户，核心是为了允许用户访问具体的门户(dify实例)，只能为没有绑定门户的用户绑定。

### 4.5.9.2 处理流程

![](images/b729fda3d91d7c0d22ceaaac72aa4813c271e0a6866084999db23b0a5e8b4f60.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张流程图，描述了一个系统或程序的处理流程。流程从顶部开始，经过一系列的判断和操作，最终到达底部的结束点。具体步骤如下：

1. **参数校验**：首先进行参数校验，如果校验不通过，则进入错误处理流程（标记为“error”），并以一个红色的叉号表示流程结束。
2. **ID列表不为空**：如果参数校验通过，则检查ID列表是否不为空。如果不为空，则继续下一步；如果为空，则进入错误处理流程，并以一个红色的叉号表示流程结束。
3. **查询管理员的门户**：如果ID列表不为空，则查询管理员的门户信息。
4. **管理员已绑定门户？**：检查管理员是否已经绑定了门户。如果是，则继续下一步；如果不是，则进入错误处理流程，并以一个红色的叉号表示流程结束。
5. **所有用户未绑定门户？**：如果管理员已绑定门户，则检查所有用户是否都未绑定门户。如果是，则进行门户绑定操作；如果不是，则进入错误处理流程，并以一个红色的叉号表示流程结束。
6. **绑定门户**：如果所有用户都未绑定门户，则进行门户绑定操作。
7. **落库**：门户绑定成功后，将数据存入数据库（标记为“落库”）。
8. **结束**：流程最终到达底部的结束点，以一个黑色的圆圈表示。

整个流程图展示了在不同条件下的处理逻辑和可能的错误路径。
```


### 4.5.9.3 接口说明

接口 URI：/\${统一路径}/portal/user/bind

请求方式：post

请求参数：

Body：  

<html><body><table><tr><td>参数名称</td><td>参数值</td><td>是否必须</td><td>实例</td><td>备注</td></tr><tr><td>userIds</td><td>List<Long></td><td>是</td><td></td><td>用户id列表</td></tr></table></body></html>

返回数据：

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr></table></body></html>

<html><body><table><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td></td><td></td><td></td></tr></table></body></html>

### 4.5.10 解绑门户管理员

### 4.5.10.1 功能描述

支持批量（一个或多个）为用户解绑门户管，核心是为了停止用户访问具体的门户(dify实例)的权利，只能为已经绑定门户的用户解绑。

### 4.5.10.2 处理流程

![](images/a5549bb120e07fdc549a88f76150ffccdfd57d9fc74560517adea06d4f67b452.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张流程图，描述了一个系统操作的步骤和决策过程。流程从顶部开始，经过参数校验、id列表不为空的判断、所选用户已绑定管理员的判断，最终到达落库或error状态。

1. 流程开始于一个黑色圆点。
2. 接下来是“参数校验”步骤。
3. 然后是对“id列表不为空”的判断：
   - 如果不通过，则流程进入“error”状态，并以一个带有红色叉号的圆圈结束。
   - 如果通过，则继续下一步。
4. 接下来是对“所选用户已绑定管理员？”的判断：
   - 如果回答为“NO”，则流程再次进入“error”状态，并以一个带有红色叉号的圆圈结束。
   - 如果回答为“YES”，则进行“解除门户信息”的操作。
5. 最后，流程到达“落库”步骤，并以一个黑色圆点结束。

这个流程图展示了一个系统操作的详细步骤和可能的错误处理路径。
```


### 4.5.10.3 接口说明

接口 URI：/\${统一路径}/portal/user/unbind

请求方式：post请求参数：

Body：  

<html><body><table><tr><td>参数名称</td><td>参数值</td><td>是否必须</td><td>实例</td><td>备注</td></tr><tr><td>userIds</td><td>List<Long></td><td>是</td><td></td><td>用户id列表</td></tr></table></body></html>

返回数据：

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td></td><td></td><td></td></tr></table></body></html>

可以根据门户id 进行解绑，主要用于删除门户时，此时需要解除门户的绑定的门户用户。

接口 URI：/\${统一路径}/portal/user/unbindByPortalId

请求方式：post

请求参数：

Body：  

<html><body><table><tr><td>参数名称</td><td>参数值</td><td>是否必须</td><td>实例</td><td>备注</td></tr><tr><td>portalId</td><td>Long</td><td>是</td><td></td><td>门户id</td></tr></table></body></html>

返回数据：

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td></td><td></td><td></td></tr></table></body></html>

### 4.5.11 获取用户详情

### 4.5.11.1 功能描述

根据用户 id 获取用户详情，主要针对内部需要查询用户信息的接口：比如根据用户 id查询该用户绑定的门户是哪个。

### 4.5.11.2 处理流程

查询接口，流程较为简单，不做赘述。

### 4.5.11.3 接口说明

接口 URI：/\${统一路径}/portal/user/get

请求方式：Get

请求参数：

userId: Long

返回数据：  

<html><body><table><tr><td>名称</td><td>类型</td><td>是否必须</td><td>备注</td></tr><tr><td>code</td><td>String</td><td>必须</td><td>错误码</td></tr><tr><td>message</td><td>String</td><td>必须</td><td>描述</td></tr><tr><td>requestId</td><td>String</td><td></td><td>请求ID</td></tr><tr><td>timestamp</td><td>String</td><td></td><td>时间戳</td></tr><tr><td>costMillis</td><td>Long</td><td></td><td>花费时间</td></tr><tr><td>result</td><td>PortalUserVO</td><td></td><td>用户id</td></tr><tr><td rowspan="4">PortalUserVO</td><td>id</td><td>Long</td><td>用户id</td></tr><tr><td>userName</td><td>String</td><td>用户名</td></tr><tr><td>portalId</td><td>Long</td><td>门户id</td></tr><tr><td>loginDate</td><td>String</td><td>最后登录时间</td></tr></table></body></html>

### 4.6 SS-02-04 门户管理

### 4.6.1 功能点清单

<html><body><table><tr><td>功能点编号</td><td>功能点清单</td><td>优先级</td></tr><tr><td>SS-02-04-01</td><td>创建门户</td><td>高</td></tr><tr><td>SS-02-04-02</td><td>编辑门户</td><td>高</td></tr><tr><td>SS-02-04-03</td><td>删除门户</td><td>高</td></tr><tr><td>SS-02-04-04</td><td>启动门户</td><td>高</td></tr><tr><td>SS-02-04-05</td><td>停止门户</td><td>高</td></tr><tr><td>SS-02-04-06</td><td>跳转管理端</td><td>高</td></tr></table></body></html>

<html><body><table><tr><td>SS-02-04-07</td><td>跳转门户</td><td>高</td></tr><tr><td>SS-02-04-08</td><td>首页配置</td><td>低</td></tr><tr><td>SS-02-04-09</td><td>访问控制配置</td><td>高</td></tr></table></body></html>

### 4.6.2 重要流程说明

以下为门户管理功能和用户权限对应关系

![](images/54bc117c113a88e23456e0f0eb982286e6630c78eb061094097caedccea13397.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个系统管理的架构图，主要分为以下几个部分：

1. **系统管理员**：位于图的左上角，负责创建门户、初始化Dify实例以及门户的启停和删除。

2. **门户管理员**：分为门户管理员-1和门户管理员-2。门户管理员由系统管理员创建，并绑定到特定的门户。门户管理员负责门户的个性化配置。

3. **门户用户**：门户用户由门户管理员创建并绑定到特定的门户。图中展示了三个门户用户：门户用户1-1、门户用户1-2和门户用户1-3。

4. **门户管理**：位于图的中间部分，包含四个门户（门户1、门户2、门户3和门户4）。每个门户可以跳转到Dify管理端进行管理。

5. **Dify实例-1**：位于图的右上角，包含三个应用（应用1、应用2和应用3）。

6. **基础网关**：位于图的底部，门户用户可以通过基础网关访问应用。

整个架构图展示了从系统管理员到门户管理员再到门户用户的层级关系，以及他们如何通过门户管理和访问应用的过程。
```


### 4.6.3 创建门户

### 4.6.3.1 功能描述

门户创建流程：

1. 系统管理员创建门户  
2. 建立门户和实例的关联，部署Dify 实例  
3. 部署进度更新到门户状态  
4. 部署成功后，要对Dify 实例周期探活，更新门户状态

门户状态机：

门户状态机

![](images/14c87acd6cd07b37171042e6673bd2d0d0337ba9cd5e193be68680ff685819c9.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个系统状态转换图，描述了从创建门户到删除门户的整个生命周期中可能遇到的各种状态及其转换条件。具体包括：

1. **创建门户**：这是流程的起点。
2. **初始化中**：创建门户后，后端开始部署Dify实例，并进行初始化。
3. **初始化失败**：如果在初始化过程中出现问题，则会进入此状态。
4. **运行中**：Dify实例运行正常时的状态。
5. **异常**：当Dify实例运行异常或启动失败时进入此状态。
6. **停止中**：手动关闭门户或Dify实例探活失败时进入此状态。
7. **已停止**：Dify实例正常关闭，门户停止提供服务时的状态。
8. **删除中**：用户提交删除请求，正在删除门户信息时的状态。
9. **删除失败**：删除过程中出现问题，需要人工介入时的状态。

此外，图片还展示了各种状态之间的转换条件，如“Dify实例探活成功”、“手动关闭门户”等，以及一些特殊情况下的处理方式，如“删除成功”和“手动启动门户成功”。
```


<html><body><table><tr><td>门户管理系统 门户管理</td><td colspan="8"></td></tr><tr><td>门户理</td><td colspan="7"></td></tr><tr><td>用户管理</td><td colspan="4">门户名 门户 请浦人门庐名种 请进绿</td><td colspan="4"></td></tr><tr><td>系统设置</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td rowspan="10"></td><td>门产ig6</td><td>门户名排</td><td>门产书班</td><td>地址换盘</td><td>首页默证应用</td><td>创建对间</td><td>门户状态</td><td></td></tr><tr><td>n</td><td></td><td>全1号门</td><td>/pone</td><td></td><td>4/26/2025,642.04AM</td><td>E</td><td></td></tr><tr><td>0</td><td>门</td><td>企业2门户平</td><td></td><td>工作</td><td>4/17/2025.120:36PM</td><td></td><td></td></tr><tr><td></td><td>门P3</td><td>企业：3号门户台</td><td>/poma</td><td>工作</td><td>4/29/2025.1:5658PM</td><td></td><td></td></tr><tr><td></td><td></td><td>4年门户平台</td><td>fporal</td><td>收分析</td><td>4/2/2025.4918PM</td><td></td><td>P5</td></tr><tr><td>n</td><td>门户：</td><td>业5号门产平位</td><td>pot</td><td>石户管理</td><td>4/16/2025.9:050PM</td><td></td><td></td></tr><tr><td>D</td><td>门户</td><td>企重东号门户平台</td><td>/pantadh</td><td>工作</td><td>4/8/2025.913.06AM</td><td></td><td></td></tr><tr><td></td><td>门P)</td><td>企业7号门户平台</td><td>/goitar7</td><td>据分柜</td><td>4/1/2025.11:1642.AM</td><td></td><td></td></tr><tr><td></td><td></td><td>业号门户</td><td>/porta</td><td></td><td>4/30/2025,5:27.40 AM</td><td></td><td></td></tr><tr><td>n</td><td>门户</td><td>生号门户中台</td><td></td><td></td><td>4/225,811.0</td><td></td><td></td></tr><tr><td>n</td><td></td><td>10门平台</td><td>iportan</td><td>分钟</td><td>4//025*55P</td><td></td><td></td></tr><tr><td></td><td>共100记</td><td></td><td></td><td></td><td>Total100 10/page</td><td></td><td>4 5 6 10 3 Gob</td></tr></table></body></html>

用户停留在门户列表页面，页面需要定时刷新列表，以更新门户状态创建门户时，提供门户的预览页面，目前支持的预览配置有：门户标题、门户logo，后期考虑增加的预览配置：

![](images/c6e46450e136d0bf02bd20cc6a1f758f513b4ea821f8911257d5642a986e7ee4.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示门户管理系统的截图，具体是创建门户的页面。
```


$\bullet$ 访问路径、登录页logo、门户主题  
$\bullet$ 知识库存储路径、邮件服务器配置  
$\bullet$ 首页欢迎语、首页默认应用、是否支持上传文件、文件类型  
$\bullet$ 是否支持会话历史记录、是否支持应用收藏

### ******* 处理流程

门户创建流程：

![](images/d530c719b917176062852a0d40c7213e7d030e582b29fcca116a2b7380987221.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个创建门户的流程图，主要涉及系统管理员、统一门户后端服务、数据库和docker api之间的交互。流程如下：

1. 系统管理员发起创建门户的请求，需要提供门户名称、门户标题、logo、访问地址前缀、选择主题、配置默认应用、门户管理员、管理员账号和管理员密码等信息。
2. 统一门户后端服务接收到创建门户的请求后，保存门户信息。
3. 创建实例信息，包括数据挂载目录、知识库、上传的图片、生成的文件、租户的私钥、向量数据库、redis、sandbox、插件、API前缀和容器名称前缀等。
4. 保存门户和实例的关联关系。
5. 执行异步初始化任务，这部分流程参见《Dify实例部署流程》。
6. 创建门户成功，状态为初始化中。
7. 系统管理员通过循环每10秒获取一次门户状态。

整个流程展示了从创建门户到门户初始化完成的详细步骤，涉及到多个组件和服务的协同工作。
```


Dify 实例部署流程：

Dify实例部署流程

![](images/9ce6458be1da522b636a01fe8b4522ef02bc080a2403b80a6af21d013717aee2.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个系统部署和初始化的流程图，主要分为两个阶段：准备阶段和实例部署阶段。在准备阶段，首先需要初始化数据库和配置文件，包括创建数据库、执行数据库初始化脚本、生成配置文件等步骤。在实例部署阶段，则是部署Dify实例，涉及检查实例前缀是否可用、创建Dify实例的各容器、查询容器状态等步骤。整个流程中还包含了错误处理机制，如更新门户状态、处理初始化失败等情况。
```


Dify 初始化阶段：

![](images/5e33b7573041051b22a6e4a3a5fa4b7bd9b8419307aa6cfaa6b1c030eb9c9c91.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个名为“Dify初始化”的流程图，详细描述了从统一门户后端服务到Dify实例的初始化过程。流程图分为几个主要步骤：

1. **系统初始化**：
   - 1-2 设置管理员：在Dify实例中设置管理员。
   - 1-3 执行结果（成功/失败）：检查设置管理员的操作是否成功。
   - 1-4 登录管理员：使用管理员账号密码登录。
   - 1-5 登录token：获取登录后的token。
   - 1-6 保存token：将token保存起来以供后续使用。

2. **配置自有模型**：
   - 1-7 查询模型框架配置：查询模型框架的配置信息。
   - 1-8 模型框架配置：根据查询结果配置模型框架。
   - 1-9 查询模型apikey：查询模型的apikey。
   - 1-10 模型apikey：获取并使用apikey。
   - loop [根据模型配置列表遍历]：
     - 1-11 模型供应商添加模型：根据配置列表添加模型。
     - 1-12 执行结果：检查添加模型的操作是否成功。

3. **配置系统模型**：
   - 1-13 查询系统模型配置：查询系统模型的配置信息。
   - 1-14 系统模型配置：根据查询结果配置系统模型。
   - 1-15 配置系统模型：执行系统模型的配置操作。
   - 1-16 执行结果：检查配置系统模型的操作是否成功。

4. **判断执行结果及返回**：
   - 1-17 判断执行结果，组装失败原因（如果失败）：检查整个初始化过程是否成功，如果不成功则组装失败原因。
   - 1-18 返回初始化结果及失败原因（如果失败）：返回初始化的结果和可能的失败原因。

流程图还展示了与Dify api、Dify实例、模型配置文件和数据库之间的交互关系，以及一些具体的配置细节，如模型访问URL、模型名称、模型类型等。
```


### 4.6.4 编辑门户

### 4.6.4.1 功能描述

系统管理员、门户管理员可以修改门户名称、门户标题、logo无法修改访问路径、管理员账号、管理员密码

![](images/8266ca6bfcf8d30d4b12f2265bd07ed8e8ff20244798f9d68ebde272f7e909b9.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个网页表单，用于设置门户的基本信息。表单包含以下几个部分：

1. **门户名称**：有一个输入框，提示用户“请输入门户名称”。
2. **门户标题**：有一个输入框，示例文本为“例如：企业统一门户、部门信息平台”，并有一个提示框说明“门户标题将展示在统一门户页的左上角和浏览器标题栏”。
3. **门户Logo**：有一个空白区域，用于上传或显示门户的Logo。
4. **访问路径**：有一个输入框，预填了“https://192.168.100.100:8888/hub/”，并有一个提示框说明“一个短小易记的URL地址有助于访问和传播”。
5. **管理员账号**：有一个输入框，用于输入管理员的账号。
6. **管理员密码**：有两个输入框，分别用于输入管理员的密码和确认密码。

在表单的底部，有两个按钮：“提交”和“预览”。
```


### 4.6.4.2 处理流程

![](images/93a34e8a557405db4a97f8fd7f19758b8f983e0163db659e2d42072a1cb6ccd3.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个编辑门户的流程图，主要涉及系统管理员和门户管理员的操作。流程如下：

1. **权限归属**：系统管理员和门户管理员拥有编辑门户的权限。
2. **编辑门户**：系统管理员或门户管理员发起编辑门户的请求，需要提供以下信息：
   - 门户ID
   - 门户名称
   - 门户标题
   - logo
   - 访问地址前缀
   - 选择主题
   - 配置默认应用
3. **更新门户信息**：统一门户后端服务接收到编辑请求后，将更新后的门户信息保存到数据库中。
4. **编辑成功**：系统管理员或门户管理员收到编辑成功的反馈。

整个流程通过统一门户后端服务与数据库进行交互，确保门户信息的准确更新和存储。
```


### 4.6.5 删除门户

功能描述

状态为创建中、运行中、删除中的门户不支持删除

删除流程：

- 在门户列表页操作“删除”门户

- 前端给出二次确认（注意：删除门户会同步删除门户下的所有数据，包括应用、历史会话、知识库、数据库等，确认删除？）

- 后端开始删除门户，并将门户状态改为“删除中”  
- 后端删除Dify 实例对应的容器  
- 后端删除Dify 实例下产生的数据：应用、历史会话、知识库、数据库  
- 后端删除实例信息、门户信息、门户和实例的绑定关系  
- 删除门户管理员账号  
- 保留门户用户账号，但删除用户和门户的绑定关系

### 4.6.5.1 处理流程

![](images/1963c870c95f7c9618301f8828aa48809d577f7bf5ebabf0b7b3315532d88dfd.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个名为“删除门户”的流程图，详细描述了系统管理员如何通过统一门户后端服务和Docker API来删除一个门户及其相关数据的过程。以下是流程图的主要步骤：

1. 系统管理员发起删除门户的请求。
2. 统一门户后端服务进行二次确认。
3. 系统管理员再次确认删除门户。
4. 判断门户状态是否支持删除。
5. 更新门户状态为“删除中”。
6. 执行异步删除任务。
7. 正在删除门户。

异步删除任务包括：
8. 查询门户对应的Dify实例信息。
9. 使用docker compose命令删除Dify实例。
10. 检查各容器是否存在。
11. 如果所有容器都不存在，则更新门户状态为“删除成功”。
12. 如果有容器存在且超过N次检查，则更新门户状态为“删除失败”。

此外，流程图还包含一个循环步骤，每隔10秒获取门户状态，直到删除操作完成。

图片顶部有一个灰色框，列出了删除门户时需要注意的五点事项，包括确认门户状态、用户二次确认、删除容器和挂载目录数据、删除实例信息和绑定关系、以及删除门户管理员并保留用户信息等。

图片底部标注了“门户状态机”，表明这是一个关于门户状态变化的流程图。
```


门户状态机

初始化中：创建门户，后端开始部署Dify实例，初始化Dify实例  
初始化失败：初始化过程中出现问题  
删除中：用户已提交删除，正在删除门户信息  
删除失败：删除过程中出现问题，需要人工介入  
运行中：DIfy实例运行正常

![](images/85655fe85fd24c3ede338ffc2e622033ed828fec021abd17be78536570f5c47e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张流程图，描述了Dify实例在不同状态下的转换过程。流程图从创建门户开始，经过初始化中、运行中、异常、已停止、删除中等状态，最终到达删除失败或删除成功。每个状态都有相应的描述和可能的转换条件，例如初始化过程中出现问题会导致初始化失败，手动关闭门户会进入已停止状态，手动启动门户成功会回到运行中状态等。
```


### 4.6.6 启动门户

### 4.6.6.1 功能描述

门户启动功能描述：

1. 启动门户时，后端自动拉起门户对应的Dify 实例  
2. 后端根据Dify 实例状态，更新门户状态  
3. 后端周期探测Dify 实例状态，并同步更新门户状态

门户状态周期探活功能描述：

后台通过定时任务，检查各门户的运行状态是否正常，业务逻辑如下：1. 根据状态机，只检查处于特定状态的门户：运行中、异常2. 定时任务每隔10s 执行一次，每次遍历所有实例，分别检查状态3. 一旦检测到实例状态发生变化，立即更新到数据库

### 4.6.6.2 处理流程

门户启动流程

启动门户

![](images/012bdf8b304d5f1d160a41133ffeaa4d8f8cb159089fec4ceccd52991b298640.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张系统流程图，描述了系统管理员启动门户时，后端服务如何与数据库和Docker API交互以启动和管理Dify实例的过程。具体步骤如下：

1. 系统管理员启动门户。
2. 统一门户后端服务根据状态机判断门户状态是否支持启动。
3. 更新门户状态为“启动中”。
4. 执行异步启动任务。
5. 查询门户对应的Dify实例信息。
6. 检查Dify实例的各容器是否存在。
7. 如果某容器不存在，则更新门户状态为“异常”。
8. 使用docker compose启动Dify实例。
9. 每隔10秒检查各容器状态。
10. 如果全部容器状态正常，则更新门户状态为“运行中”。
11. 如果有容器存在且超过N次，则更新门户状态为“异常”。
12. 系统管理员每隔10秒获取门户状态。

整个流程涉及系统管理员、统一门户后端服务、数据库和Docker API之间的交互，确保门户的正确启动和状态更新。
```
  
门户状态周期探活流程

门户周期探活

![](images/bdf7005787c4ce8fadfd8b3dfa9576962d146d002c4bef8219fb5bb5120253a8.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个门户周期探活的流程图，描述了后台通过定时任务检查各门户运行状态的业务逻辑。具体步骤如下：

1. 定时任务每隔10秒触发一次，检查处于特定状态（运行中、异常）的门户。
2. 根据门户状态查询门户列表。
3. 遍历门户列表，查询每个门户对应的Dify实例信息。
4. 检查Dify实例的各容器是否存在。
5. 检查各容器的状态。
6. 根据容器状态更新门户状态：
   - 如果所有容器状态正常，则更新门户状态为“运行中”。
   - 如果有容器不存在或状态异常，则更新门户状态为“异常”。
7. 执行结束后，返回到定时任务。

整个流程涉及到统一门户后端服务、数据库和docker api的交互。
```


### 4.6.7 停止门户

### 4.6.7.1 功能描述

停止门户时，后端也一并停止门户对应的 Dify 实例；  
用户无法访问已停止的门户（404）  
DIfy 实例停止后，实例对应的容器依然存在，不会删除，下次启动门户时使用

### 4.6.7.2 处理流程

![](images/df92a3d76cf12f5037b6fd74d4e236d5dde47d808d6b45cc580e6b4f31dc6406.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个系统管理员停止门户的流程图。流程如下：

1. 系统管理员通过门户ID向统一门户后端服务发送停止门户的请求。
2. 统一门户后端服务通过docker compose命令尝试停止对应的Dify实例。
3. 如果停止失败，统一门户后端服务会返回停止失败的信息给系统管理员，并在数据库中更新门户状态为“已停止”。
4. 如果停止成功，系统管理员会收到停止成功的通知。

图片中还提到，权限归属为系统管理员，在停止门户时，后端也会一并停止门户对应的Dify实例。
```


### 4.6.8 跳转管理端&跳转门户

### 4.6.8.1 功能描述

管理端和统一门户的URL 由后台维护，后端提供URL 查询接口当用户点击跳转按钮时，前端先根据门户 ID 查询对应的 URL，然后打开URL 即可

### 4.6.8.2 处理流程

跳转管理端&跳转统一门户

![](images/aa99c1489413b62111fa3d0c850fcf91af1fd5d3622e267e0ae2bc0a8f15502d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个系统架构图，描述了管理员通过Dify管理端和统一门户与统一门户后端服务之间的交互流程。具体步骤如下：

1. 管理员点击跳转管理端或跳转统一门户。
2. 系统查询URL。
3. 向数据库查询门户信息。
4. 根据前缀拼接URL。
5. 返回URL给管理员。
6. 如果是管理端，则打开相应的URL。
7. 如果是统一门户，则打开相应的URL。

整个过程涉及到管理员、Dify管理端、统一门户、统一门户后端服务和数据库之间的交互。管理员通过点击跳转按钮触发流程，系统根据门户ID查询对应的URL，并最终打开该URL。
```


### 4.6.9 首页配置（迭代2）

### 4.6.9.1 功能描述

该功能用于用户访问统一门户首页时，默认展示一个问答式应用，方便用户快速问答1. 平台拉取Dify 实例中的现有问答式应用列表

2. 选择某个应用保存后，用户访问门户时，首页中使用的应用即默认应用该功能放在迭代2 中实现，在迭代1 中首页默认应用由系统预置，不可更改

### 4.6.9.2 处理流程

### 4.7 SS-02-05 统一门户

### 4.7.1 功能点清单

<html><body><table><tr><td>功能点编号</td><td>功能点清单</td><td>优先级</td></tr><tr><td>SS-02-05-01</td><td>对话</td><td>高</td></tr><tr><td>SS-02-05-02</td><td>会话历史</td><td>高</td></tr><tr><td>SS-02-05-03</td><td>删除会话</td><td>高</td></tr><tr><td>SS-02-05-04</td><td>会话重命名</td><td>高</td></tr><tr><td>SS-02-05-05</td><td>非对话类应用页面内嵌</td><td>高</td></tr><tr><td>SS-02-05-06</td><td>应用探索页</td><td>高</td></tr></table></body></html>

### 4.7.2 重要流程说明

### 4.7.3 首页对话

### 4.7.3.1 功能描述

功能描述

SWXA-CMMI-SD-30

![](images/3d7298838428051ad2565cfe82a054f2076a5a98da80c0f83aded61caa42ac42.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个名为“SANSEC智能助手”的聊天界面。界面左侧有一个功能菜单，列出了多个选项，包括应用探索、历史对话、团队协作效率提升、市场营销数据分析、学术探索、PPT助手等。右侧是聊天窗口，显示了一条来自智能助手的消息：“你好！我是sansec智能助手，有什么需要解答的，随时告诉我哦~”。聊天窗口上方有一个红色标注，提示新建会话即打开默认应用的chat页，并且按照dify开源协议，logo不能去掉。聊天窗口下方有一个输入框，标有“Talk to Bot”，用户可以在此输入消息与智能助手进行交流。此外，图片右下角还有一个红色标注，说明内嵌页面的URL由后端返回，并提供了相关的技术细节。
```


![](images/86c0a943b4309a09ceec3df410e6261d1aed78ab588b65e46db710239e61fbda.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个名为“SANSEC智能助手”的应用程序界面。界面左侧有一个导航栏，列出了多个功能选项，包括“新建会话”、“应用探索”、“市场营销数据分析”等。当前选中的功能是“市场营销数据分析”。

在右侧的主内容区域，有一个聊天窗口，显示了与SANSEC智能助手的对话。智能助手的回复内容包括自我介绍和功能说明，提到它是通义千问，阿里巴巴集团旗下的超大规模语言模型，能够回答问题、创作文字、表达观点、玩游戏等，并支持多种语言。

聊天窗口底部有一个输入框，标有“Talk to Bot”，用户可以在此输入信息与智能助手进行交流。此外，图片中还标注了一些技术细节，如“应用icon”、“应用名称”、“内嵌页面”以及前端需要修改localStorage中的会话id为当前会话id的提示。
```


### 4.7.3.2 处理流程

获取内嵌 chat 页面 URL

![](images/070b4691d678bc12e8a9f35c489b1fd296e020a9424fa13a29acd8a38fc5a5fc.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个获取内嵌chat页面URL的流程图，涉及前端、统一门户后端服务、Dify api和Dify实例四个部分。具体步骤如下：

1. 前端发起请求，查询内嵌chat页面URL，需要提供当前登录用户id（user_id）和应用id（app_id），其中app_id为空则代表默认应用。
2. 统一门户后端服务根据用户获取所属门户，并调用获取URL接口。
3. Dify api在单例模式下获取dify实例token，首先查询内存中是否包含该门户的token，如果token快过期，则需要更新token，通过登录管理员账户获取新的token，并更新内存中的token。
4. 获取应用的公开访问URL，执行结果，并返回给前端。
5. 对user_id进行compressAndEncodeBase64操作，拼接sys.user_id到URL中，最终返回结果。

整个流程涉及到多个步骤和组件之间的交互，确保了内嵌chat页面URL的安全性和有效性。
```


### 4.7.4 会话历史

### 4.7.4.1 功能描述

功能描述：

1. 左侧菜单栏的历史会话列表包括：$\blacktriangle$ 用户使用对话类应用时，产生的历史会话列表

2. 用户点击某个历史会话，右侧对话框展示该会话下的历史消息，可以继续对话。

3. 用户点击某个应用时进入新建会话页面，点击“新建会话”按钮会进入默认应用的新建会话页面。在新建会话页面进行第一次交互后，该会话会自动重命名并进入历史对话列表里。（前端不知道内嵌页面何时进行第一次交互，是否可以：每次点击新建会话或其他历史会话时调用应用列表接口刷新列表）

4. 在新建会话页面不进行任何交互直接关闭，则不进入历史对话列表。

5. 非对话类应用不进入历史对话列表。

![](images/ecd45788983bb8e5e0ce6c4a7ccd258dda3a4467463eaf44799aaa1505cf4936.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个名为“SANSEC智能助手”的应用程序界面。界面左侧有一个导航栏，列出了多个功能选项，包括“新建会话”、“应用探索”、“市场营销数据分析”等。当前选中的功能是“市场营销数据分析”。

在右侧的主内容区域，显示了一个聊天窗口，其中包含一个智能助手的对话记录。智能助手的名称是“SANSEC智能助手”，它正在与用户进行交流。对话框中显示了智能助手的问候语和自我介绍，内容包括它的功能、训练数据的时间范围以及支持的语言等。

此外，图片中还标注了一些关键元素的位置，如“应用icon”、“应用名称”、“内嵌页面”等，并提示前端需要修改localStorage中的会话id为当前会话id。
```


每个用户最多可保存100 个历史会话，单个会话最多100 次交互。

### 4.7.4.2 处理流程

![](images/3539e5f349c131ea204945297e23be2ef8b4806ba81f156fda2dbe558351f41c.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个获取历史会话列表的流程图，涉及前端、统一门户后端服务、Dify api和Dify实例四个部分。流程从前端发起请求开始，经过统一门户后端服务处理，最终通过Dify api和Dify实例返回历史会话列表给前端。图片中还详细描述了改造Dify接口前后的变化，以及获取历史会话列表所需参数和返回数据的具体内容。
```


### 4.7.5 删除会话

### 4.7.5.1 功能描述

用户可以主动删除会话

### 4.7.5.2 处理流程

![](images/8313bbc6f678d470a8808c7dcd40cff930d40d2c10562fc3a4bc3fbffc73f3c5.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个系统架构图，描述了前端、统一门户后端服务、Dify api和Dify实例之间的交互流程。具体步骤如下：

1. 前端调用删除会话接口（1-1），并传递当前登录用户id、应用id和会话id。
2. 统一门户后端服务根据用户获取所属门户（1-2）。
3. 统一门户后端服务调用删除会话接口（1-3）。
4. Dify api查询内存中是否包含该门户的token（1-4）。
5. 检查token是否快过期（1-5）。
6. 如果需要更新token，则登录管理员账户获取新的token（1-6）。
7. 获取新的token（1-7）。
8. 更新内存中的token（1-8）。
9. 根据应用id获取应用的api密钥（1-9）。
10. 获取api密钥列表（1-10）。
11. 如果api密钥列表为空，则创建api密钥（1-11）。
12. 获取api密钥内容（1-12）。
13. 调用删除会话接口（1-13）。
14. 执行结果（1-14）。
15. 将执行结果返回给统一门户后端服务（1-15）。
16. 最终将执行结果返回给前端（1-16）。
```


### 4.7.6 会话重命名

### 4.7.6.1 功能描述

用户可以主动对会话进行重命名

### 4.7.6.2 处理流程

![](images/c6bc82a53c754e2329e9e819326fb2752d0eadfc200d4688383ad4e66f50ebbd.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个会话重命名的流程图，涉及前端、统一门户后端服务、Dify api和Dify实例四个部分。流程从前端开始，用户需要提供user_id（当前登录用户id）、app_id（应用id）、conversation_id（会话id）和new_name（新会话名称）。然后，前端调用删除会话接口，统一门户后端服务根据用户获取所属门户，并再次调用删除会话接口。接下来，流程进入单例模式获取dify实例token的步骤，包括查询内存中是否包含该门户的token、检查token是否快过期、登录管理员账户获取新的token以及更新内存中的token。之后，流程获取应用的api密钥，根据应用id获取api密钥列表，如果列表为空，则创建api密钥并获取其内容。最后，调用会话重命名接口并返回执行结果给前端。
```


### 4.7.7 非对话类应用页面内嵌

### 4.7.7.1 功能描述

非对话类应用页面直接内嵌，不会进入历史会话列表

![](images/1a38f629493031667d0a114ea611935ac18d8e6f64d03d753ceb5c49e3b5177a.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个名为“SANSEC智能助手”的网页界面，主要功能是小说生成器。页面左侧有一个菜单栏，列出了多个功能选项，包括应用探索、市场营销数据分析、学术探索、PPT助手、团队协作效率提升、竞品分析报告生成等。中间部分是一个小说生成器的界面，用户可以输入小说主题并选择运行一次或批量运行。当前输入的小说主题是“九子夺嫡”。右侧显示了生成的小说内容，标题为《扳指》，内容涉及康熙四十二年腊月的故事，描述了太子胤礽在紫禁城的情景以及与九皇子胤禟的互动。页面底部有提示信息，说明文本生成类chat不产生历史会话记录，只能通过应用探索页面点击应用进入，并且内嵌页面不需要改Localstorage的会话id。
```


### 4.7.8 门户应用加载

### 4.7.8.1 功能描述

![](images/6834eb4b270704a3c394c44c2c508b027d73da67fb63760c9347a18732cc2a37.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个名为“SANSEC智能助手”的应用程序界面。界面左侧有一个导航栏，列出了多个功能选项，包括“新建会话”、“应用探索”、“市场营销数据分析”、“学术探索”、“PPT助手”等。右侧是“探索 SANSEC+”的页面，显示了四个主要功能模块：

1. **SANSEC 探索版**：问题太复杂？用探索版慢慢看。
2. **长文生成器**：你说一句，我写万字长文。
3. **PPT 助手**：Kemi + AiPPT = 一键生成PPT。
4. **学术搜索**：学术问题？来问我！

每个功能模块都有一个图标和简短的描述，用户可以通过点击这些模块进入相应的功能页面。右上角显示了当前用户的名称“Sarah Johnson”。
```


### ******* 处理流程

![](images/07808361d430519729eadd11e64c071c186a8beab1c7fad6581dbb225c1b376f.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个获取应用列表的流程图，涉及前端、统一门户后端服务、Dify api和Dify实例四个部分。具体步骤如下：

1. 前端发起请求，通过user_id（当前登录用户id，必须）和应用类型（聊天应用、智能体……空则代表全部）来获取应用列表。
2. 统一门户后端服务接收到请求后，根据用户查询所属门户。
3. 获取门户下所有应用。
4. 在单例模式下获取dify实例token，首先查询内存中是否包含该门户的token，然后检查token是否快过期。
5. 如果需要更新token，则登录管理员账户获取新的token，并更新内存中的token。
6. 通过接口/console/api/apps进行查询。
7. 获取查询结果。
8. 对数据进行处理。
9. 最终将应用列表返回给前端。

整个流程确保了应用列表的准确获取和token的有效管理。
```


### 4.7.9 门户应用访问

### 4.7.9.1 功能描述

点击应用探索页里的任意应用，跳转至该应用的新建对话页。内嵌页面 URL 由后端返回。

### 4.7.9.2 处理流程

![](images/33967d1ca1777c87886f4af2f5b6652e3fbedef6ca48965b5f7e1ce6b60fd94a.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个获取内嵌chat页面URL的流程图，涉及前端、统一门户后端服务、Dify api和Dify实例四个部分。具体步骤如下：

1. 前端根据当前登录用户id（user_id）和应用id（app_id），查询内嵌chat页面URL。
2. 统一门户后端服务根据用户获取所属门户，并调用获取URL接口。
3. Dify api查询内存中是否包含该门户的token，检查token是否快过期，如果需要则更新token。
4. 更新token的过程包括登录管理员账户获取新的token，并更新内存中的token。
5. 获取应用的公开访问URL，并执行结果。
6. 对user_id进行compressAndEncodeBase64操作，拼接sys.user_id到URL中。
7. 最终返回结果给前端。

整个流程确保了安全和高效地获取内嵌chat页面的URL，同时处理了token的有效性和安全性问题。
```


# 第五章 数据围栏系统设计

### 5.1 数据围栏系统简介

数据围栏系统包含数据脱敏与内容审核组件两部分。

数据脱敏用于保障企业的数据安全和隐私安全，提供一系列针对敏感数据的识别和处置方案，其中包括敏感数据识别算法，数据脱敏处理方式，业务自定义的配置选项和海量数据处理能力。数据围栏系统用于实时检测大模型的输出内容是否涉及政治、暴力、色情等敏感信息并对敏感信息进行拦截，满足相关法律法规及企业安全的要求。

一体机集成开源组件 godlp 提供数据脱敏的能力。该组件能够广泛支持结构化（JSON数据、KV 数据、golang map）和非结构化数据（多语言字符串）；内置多种敏感数据识别规则，能对原始数据进行敏感类型识别，确保敏感信息能被妥善处理；支持多种脱敏算法，业务可以根据需求对敏感数据进行不同的脱敏处理；

godlp 默认支持手机号、邮箱地址、中国身份证号、借记卡号、信用卡号、中国护照、中文地址、MAC 地址、银行分支机构号、中国驾照、比特币钱包地址、域名、IP 地址、姓名、生日、年龄、学历、国籍、经纬度信息、电话号码等内容进行脱敏。除默认的敏感信息识别和处理规则外，业务可以根据实际情况，配置自定义的YAML 规则。

一体机集成开源安全检测工具 ShieldLM 帮助检测大型语言模型生成内容中的安全问题。该工具支持检测多种不安全内容，例如有毒和有偏见的生成内容、对身心有害的内容、非法和不道德的内容、涉及侵犯隐私的内容、可能导致财产损失的内容以及涉及敏感话题的内容。

### 5.2 功能点清单

<html><body><table><tr><td>功能点编号</td><td>功能点清单</td><td>优先级</td></tr><tr><td>SS-02-08-01</td><td>数据脱敏与内容审核组件集成</td><td>高</td></tr><tr><td>SS-02-08-02</td><td>数据围栏系统启用与禁用</td><td>高</td></tr></table></body></html>

### 5.3 重要流程说明

Dify 实例中的预置应用默认启用数据围栏。用户自定义应用可根据实际需要选择是否启用（默认启用）。在应用列表中可针对每个应用启用和禁用数据围栏系统。

数据围栏系统包含 ShieldLM 内容审核组件和 godlp 数据脱敏组件两部分，采用 docker容器形式部署，对外提供restful api。

若启用数据围栏，则由智能应用统一门户调用 dify 的 api 接口将数据围栏的 restful api设置到应用敏感内容审查api 扩展点。dify 会在大模型响应后将输出内容以http post 请求的方式发送到数据围栏系统进行安全检查和数据脱敏。若ShieldLM 判断大模型输出内容违规，则向 dify 应用返回预置内容；若 ShieldLM 判断大模型输出内容安全，则进一步由 godlp 组件进行数据脱敏，最终返回数据脱敏后的内容。

![](images/ddc1b6ff6ad8a99905e620eb68be26994e39546dcdca0146d264053c296bfdc2.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个数据围栏系统的架构图，主要分为两个部分：Dify应用列表和数据围栏系统。

## Dify应用列表
- **预置应用**：有两个预置应用，分别用橙色和紫色图标表示。这些应用的开关处于关闭状态。
- **用户应用**：有两个用户应用，分别用粉色和绿色图标表示。这些应用的开关处于开启状态。
- **http post**：用户应用通过HTTP POST请求与RESTful API进行通信。

## 数据围栏系统
- **RESTful API**：接收来自用户应用的HTTP POST请求，并处理这些请求。
- **ShieldLM**：负责大模型输出内容的安全检查。如果内容不安全，则替换大模型输出。
- **godlp**：负责数据脱敏，确保输出内容的安全性。
- **docker**：表示整个数据围栏系统运行在Docker容器中。

## 其他信息
- **数据围栏默认启用，可禁用**：说明数据围栏系统默认是启用的，但可以根据需要禁用。

这张图展示了数据围栏系统如何通过RESTful API、ShieldLM和godlp等组件来确保数据的安全性和合规性。
```


### 5.4 数据脱敏与内容审核组件集成

## 5.4.1 功能描述

该功能点需要将数据脱敏组件与内容审核组件集成到一个 docker 镜像中，开发 restfulapi 服务用于接收和响应数据围栏服务调用请求。

## 5.4.2 处理流程

restful api 服务接收 dify 应用发起的 http post 请求，将大模型的输出交由 ShieldLM 判断  
是否涉及敏感内容。若内容敏感，则对大模型的输出内容进行拦截，向 dify 应用返回预置  
回复；若不涉及敏感内容，则进一步由godlp 组件进行数据脱敏，最终返回数据脱敏后的内  
容。内容审核组件与数据脱敏组件间的交互通过函数调用方式实现。http post 请求与响应格式为：  
{"point": "app.moderation.output", // 扩展点类型，此处固定为 app.moderation.output"params": {"app_id": string,  // 应用 ID"text": string  // LLM 回答内容。当 LLM 输出为流式时，此处为 100 字为一个  
分段的内容。}  
}  
{"flagged": bool,  // 是否违反校验规则"action": string, // 动作，direct_output 直接输出预设回答; overridden 覆写传入变量值"preset_response": string,  // 预设回答（仅当 action $\circleddash$ direct_output 返回）

"text": string  // 覆写的 LLM 回答内容。（仅当 action $\circleddash$ overridden 返回）}

### 5.5 数据围栏系统启用与禁用

## 5.5.1 功能描述

该功能点需要实现预置应用默认启用数据围栏，并支持用户创建自定义应用时可以选择是否启用数据围栏。能够在页面上显示应用是否启用数据围栏，允许针对每个应用启停数据围栏功能。

## 5.5.2 处理流程

启用数据围栏时，智能应用统一门户调用 dify 的api 接口将数据围栏的restful api 设置到应用敏感内容审查api 扩展点；禁用数据围栏时，应清除敏感内容审查api 扩展点。

# 第六章 数据库设计

### 6.1 数据库表清单

门户用户表：portal_user  

<html><body><table><tr><td>Name</td><td>Comment</td><td>Default Value</td><td>Data Type</td></tr><tr><td>id</td><td>物理主键</td><td>无</td><td>BIGINT</td></tr><tr><td>user_name</td><td>用户名称</td><td>无</td><td>varchar(64)</td></tr><tr><td>user_pwd</td><td>用户密码</td><td>无</td><td>varchar(255)</td></tr><tr><td>login_date</td><td>最后登录时间</td><td>无</td><td>timestamp</td></tr><tr><td>create_time</td><td>创建时间</td><td>无</td><td>timestamp</td></tr><tr><td>update_time</td><td>修改时间</td><td>无</td><td>timestamp</td></tr><tr><td>create_by</td><td>创建者</td><td>无</td><td>varchar(64)</td></tr><tr><td>update_by</td><td>修改者</td><td>无</td><td>varchar(64)</td></tr><tr><td>flag_del</td><td>删除标记</td><td>0</td><td>tinyint(1)</td></tr></table></body></html>

用户-门户关联表：portal_user_relation  

<html><body><table><tr><td>Name</td><td>Comment</td><td>Default Value</td><td>Data Type</td></tr><tr><td>id</td><td>物理主键</td><td>无</td><td>BIGINT</td></tr><tr><td>portal_id</td><td>用户名称</td><td>无</td><td>BIGINT</td></tr></table></body></html>

<html><body><table><tr><td>user_id</td><td>用户密码</td><td>无</td><td>BIGINT</td></tr><tr><td>create_time</td><td>创建时间</td><td>无</td><td>timestamp</td></tr><tr><td>update_time</td><td>修改时间</td><td>无</td><td>timestamp</td></tr><tr><td>create_by</td><td>创建者</td><td>无</td><td>varchar(64)</td></tr><tr><td>update_by</td><td>修改者</td><td>无</td><td>varchar(64)</td></tr><tr><td>flag_del</td><td>删除标记</td><td>0</td><td>tinyint(1)</td></tr></table></body></html>

网关路由表：gateway_route  

<html><body><table><tr><td>Name</td><td>Comment</td><td>Default Value</td><td>Data Type</td></tr><tr><td>id</td><td>物理主键</td><td>无</td><td>BIGINT</td></tr><tr><td>route_id</td><td>路由 ID，唯一标识一个路由配置</td><td>无</td><td>VARCHAR(255)</td></tr><tr><td>uri</td><td>目标URI，表示请求将被转发到的目标地址</td><td>无</td><td>VARCHAR(255)</td></tr><tr><td>predicates</td><td>断言条件</td><td>无</td><td>VARCHAR(255)</td></tr><tr><td>filters</td><td>过滤器</td><td>NULL</td><td>VARCHAR(255)</td></tr><tr><td>order_num</td><td>路由顺序，用于确定多个路由匹配时的优先级</td><td>无</td><td>INTEGER</td></tr><tr><td>enabled</td><td>启用标志，表示该路由配置是否生效</td><td>无</td><td>BOOLEAN</td></tr><tr><td>create_time</td><td>创建时间</td><td>NULL</td><td>TIMESTAMP</td></tr><tr><td>update_time</td><td>修改时间</td><td>NULL</td><td>TIMESTAMP</td></tr><tr><td>create_by</td><td>创建者</td><td>NULL</td><td>VARCHAR(64)</td></tr><tr><td>update_by</td><td>修改者</td><td>NULL</td><td>VARCHAR(64)</td></tr><tr><td>remark</td><td>备注</td><td>无</td><td>VARCHAR(255)</td></tr><tr><td>flag_del</td><td>删除标记， 默认为未删除（FALSE)</td><td>FALSE</td><td>BOOLEAN</td></tr></table></body></html>

门户信息表  

<html><body><table><tr><td>字段</td><td>名称</td><td>数据类型</td><td>是否 主键 /</td><td>是否 可为 空</td></tr><tr><td>ID</td><td>ID</td><td>BIGINT[长整形]</td><td>TRUE</td><td>TRUE</td></tr><tr><td>portal_name</td><td>门户名称</td><td>VARCHAR[字串]</td><td>FALSE</td><td>TRUE</td></tr><tr><td>portal_code</td><td>门户编码/URL 前缀/实例前缀</td><td>VARCHAR[字串]</td><td>FALSE</td><td>TRUE</td></tr><tr><td>portal_title</td><td>门户标题</td><td>VARCHAR[字串]</td><td>FALSE</td><td>TRUE</td></tr><tr><td>portal_logo</td><td>门户 logo，base64</td><td>TEXT[大文本]</td><td>FALSE</td><td>TRUE</td></tr><tr><td>instance_type</td><td>实例类型**1-Dify**</td><td>INT[整数]</td><td>FALSE</td><td>TRUE</td></tr><tr><td>instance_id</td><td>实例ID</td><td>BIGINT[长整形]</td><td>FALSE</td><td>TRUE</td></tr><tr><td>default_app_id</td><td>默认应用 ID</td><td>VARCHAR[字串]</td><td>FALSE</td><td>TRUE</td></tr></table></body></html>

<html><body><table><tr><td>status</td><td>门户状态**1-初始化中 2-初始化失 败 3-运行中 4-已停止5-启动中 6- 异常 7-删除中 8-删除失败**</td><td>INT[整数]</td><td>FALSE</td><td>TRUE</td></tr><tr><td>flag_del</td><td>删除标识**0-正常 1-删除**</td><td>INT[整数]</td><td>FALSE</td><td>TRUE</td></tr><tr><td>remark</td><td>备注</td><td>VARCHAR[字串]</td><td>FALSE</td><td>FALSE</td></tr><tr><td>create_by</td><td>创建人</td><td>BIGINT[长整形]</td><td>FALSE</td><td>FALSE</td></tr><tr><td>create_time</td><td>创建时间</td><td>VARCHAR[字串]</td><td>FALSE</td><td>FALSE</td></tr><tr><td>update_by</td><td>更新人</td><td>BIGINT[长整形]</td><td>FALSE</td><td>FALSE</td></tr></table></body></html>

Dify 实例信息表  

<html><body><table><tr><td>字段</td><td>名称</td><td>数据类型</td><td>是否主 键</td><td>是否可为 空</td></tr><tr><td>ID</td><td>ID</td><td>BIGINT[长整形]</td><td>TRUE</td><td>TRUE</td></tr><tr><td>portal_id</td><td>门户 ID</td><td>BIGINT[长整形]</td><td>FALSE</td><td>TRUE</td></tr><tr><td>portal_code</td><td>门户编码/URL 前缀/实例前缀</td><td>VARCHAR[ 字 串]</td><td>FALSE</td><td>TRUE</td></tr><tr><td>instance_ip</td><td>实例访问 IP</td><td>VARCHAR[ 字 串]</td><td>FALSE</td><td>TRUE</td></tr><tr><td>instance_port</td><td>实例访问端口</td><td>VARCHAR[ 串]</td><td>FALSE</td><td>TRUE</td></tr><tr><td>secret_key</td><td>实例初始化秘钥</td><td>VARCHAR[ 字 串]</td><td>FALSE</td><td>TRUE</td></tr><tr><td>user_account</td><td>实例管理员账号</td><td>VARCHAR[ 字 串]</td><td>FALSE</td><td>FALSE</td></tr><tr><td>user_password</td><td>实例管理员密码</td><td>VARCHAR[ 字 串]</td><td>FALSE</td><td>FALSE</td></tr><tr><td>flag_del</td><td>删除标识**0-正常 1-删除**</td><td>INT[整数]</td><td>FALSE</td><td>TRUE</td></tr><tr><td>remark</td><td>备注</td><td>VARCHAR[ 字 串]</td><td>FALSE</td><td>FALSE</td></tr><tr><td>create_by</td><td>创建人</td><td>BIGINT[长整形]</td><td>FALSE</td><td>FALSE</td></tr><tr><td>create_time</td><td>创建时间</td><td>VARCHAR[ 字 串]</td><td>FALSE</td><td>FALSE</td></tr><tr><td>update_by</td><td>更新人</td><td>BIGINT[长整形]</td><td>FALSE</td><td>FALSE</td></tr><tr><td>update_time</td><td>更新时间</td><td>VARCHAR[ 串]</td><td>FALSE</td><td>FALSE</td></tr></table></body></html>

### 6.2 数据库表之间的关系

Tportal_info enabled:BOOLEAN   
ETE   
Tdity_instance_info portal_user_relation   
id:bigint idbigint sys_job_log   
ptaiNTEGER peedt job log id bigint   
flag_delINTEGER create_time:TIMESTAMP job_id:bigint update_time:TIMESTAMP

O accounts ①enants 配置 apps uuid ntitui datasets providers tenant_aocount _joins ld:uud provider_modals updated_by.uid Q mants nant_ic ui Tapp_model_confgs dataset_ict uid document_sogn onts messges duuid disabled_byuid created_by uuid parent_message_id.uuid

### 6.3 实体集列表

无

# 第七章 用户界面设计

### 7.1 基本原则

【说明】指出基本风格、屏幕总体布局和输入/输出的常规手段。

### 7.2 设计概述

【说明】本节的内容也可以归入附录“设计与编程规范、惯例及约定”中，而不在这里叙述。

一般地讲，界面设计应包括以下几项：

□屏幕环境设置  
□字型与字体  
□颜色  
□提示  
□菜单(Menu)  
□按钮（Command Button）  
□图标  
□列表框  
□常用键  
□窗口定义  
□日期类型  
□界面语言  
□其它

# 第八章 性能设计

### 8.1 大模型框架性能（迭代 2）

## 8.1.1 大模型推理性能分析

随着百亿到万亿参数大语言模型（LLM）的广泛应用，推理效率成为制约落地的核心瓶颈。传统自回归生成过程包含两个关键阶段：预填充阶段（Prefill Phase）和解码阶段（Decode Phase）。这两个阶段在计算特性和资源需求上存在显著差异，共同构成了完整的推理流程。预填充阶段对输入序列进行并行编码，解码阶段只能一个一个Token 进行生成，无法进行并行运算，并且事先不知道生成长度。为了减少解码阶段的计算量，预填充阶段需要保留token 对应的KV-Cache，解码解码依赖该缓存以串行方式生成每个新Token。

预填充阶段是整个推理过程的第一步，也是基础性环节。在这个阶段，模型需要一次性处理所有输入的 token 序列。由于输入的所有 token 都是已知且同时可用的，这使得预填充阶段能够充分利用并行计算的优势。预填充阶段的主要过程如下图所示，具体来说，输入序列中的每个 token（例如 T0、T1、T2）都会通过模型的前向计算，生成对应的查询向量 Q（q0, q1, q2）、键向量 $\textbf { \textit { K } } ( \mathbf { \mathbf { k } } 0 , \mathbf { \mathbf { k } } 1 , \mathbf { \mathbf { k } } 2 )$ ）和值向量V（v0, v1, v2）。在这个过程中，每个token的查询向量会与之前所有token 的键向量计算注意力权重，因此形成的注意力矩阵是一个严格的下三角矩阵。这个下三角矩阵随后会与值向量进行矩阵乘法运算，最终产生新的 token表示。特别值得注意的是，预填充阶段会将这些计算得到的键向量和值向量缓存起来，形成所谓的 KV-Cache（图中红框部分）。这个缓存机制对于后续的解码阶段至关重要，因为它避免了在生成过程中重复计算这些中间结果，从而显著提高了整体效率。

![](images/003fbf5831738c26c8ac8833bb844abd62d96375a758c4a439e70980e16495b3.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个计算过程的流程图，涉及到矩阵运算和向量乘法。具体步骤如下：

1. **输入数据**：左侧有三个输入数据块，分别标记为T0、T1和T2。
2. **转换为向量**：这些输入数据被转换为三个向量q0、q1和q2。
3. **与键值对进行点积运算**：接下来，这些向量与一组键值对（k0、k1、k2）进行点积运算，生成一个矩阵，矩阵中的元素是向量和键值对的点积结果。
4. **与值向量相乘**：然后，这个矩阵与另一个向量v（包含v0、v1、v2）进行相乘运算。
5. **输出结果**：最终输出是一个向量，其元素是上述矩阵与向量v相乘的结果，具体为q1k0*v0 + q1k1*v1 和 q2k0*v0 + q2k1*v1 + q2k2*v2。

这个流程图可能用于描述某种机器学习或深度学习算法中的注意力机制或类似的计算过程。
```


解码阶段则是推理过程的第二个关键环节，也是通常耗时最长的部分。与预填充阶段不同，解码阶段采用的是自回归（auto-regressive）的方式逐个生成 token，因此不能像 prefill阶段那样能做大段 prompt 的并行计算。解码阶段的主要过程如下图所示。这个过程开始于预填充阶段的最后一个输出token，作为解码阶段的初始输入（例如T3）。在解码阶段的每一步，模型都会基于当前的输入token 生成对应的查询向量Q（q3）、键向量K（k3）和值向量V（v3）。由于此时只需要计算最新token 的注意力权重，因此可以大大简化计算过程。具体来说，模型只需要计算注意力矩阵的最新一行，然后将这一行与所有缓存的值向量进行加权求和。例如，在生成第一个输出 token 时，其表示可以表示为 ${ \mathrm { q } } 3 { \mathrm { k } } 0 ^ { * } { \mathrm { v } } 0 \ { \mathrm { ~ + ~ } } { \mathrm { q } } 3 { \mathrm { k } } 1 ^ { * } { \mathrm { v } } 1 \ { \mathrm { ~ + ~ } }$ $\mathrm { q } 3 \mathrm { k } 2 ^ { * } \mathrm { v } 2 + \mathrm { q } 3 \mathrm { k } 3 ^ { * } \mathrm { v } 3$ ，其中q3、k3、v3 是当前步计算得到的，而其他向量都来自预填充阶段建立的KV-Cache。更重要的是，解码阶段的每一步都会将新生成的键值对追加到KV-Cache中（图中红框部分），为下一步的计算做好准备。这个过程会循环往复，直到模型生成终止符或者达到预设的最大长度限制。

![](images/79af031d477a76462a0ffcd7f1e24b190c5c08dbf9b838194a2ba8979636619d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个计算过程的示意图。图中包含以下几个部分：

1. **T3节点**：图的左侧有两个标记为“T3”的紫色方框，它们通过蓝色箭头连接到右侧的“k3”节点。
2. **k3节点**：在图的中间部分，有一个标记为“k3”的紫色方框，它被红色边框包围，表示这是一个关键节点。从左侧的T3节点有一条蓝色箭头指向这个k3节点。
3. **q3矩阵**：在k3节点的右侧，有一个4x1的矩阵，标记为“q3”，其中包含了四个元素：q3k0、q3k1、q3k2和q3k3。
4. **v3节点**：在图的最右侧，有一个标记为“v3”的紫色方框，它被红色边框包围，表示这也是一个关键节点。
5. **计算过程**：在q3矩阵和v3节点之间，有一个蓝色的叉号，表示这是一个乘法操作。右侧的蓝色箭头指向一个公式，该公式展示了计算过程：q3k0*v0 + q3k1*v1 + q3k2*v2 + q3k3*v3。

整个图示表示的是一个计算过程，其中T3节点通过某种方式影响到k3节点，然后k3节点与q3矩阵进行乘法运算，最终得到v3节点的结果。
```


在实际应用中，解码阶段的耗时往往占据了整个推理过程的绝大部分，约占总推理时长的 $90 \%$ 左右。这种时间分布的不均衡主要源于两个因素：首先，解码阶段必须采用串行方式逐个生成token，无法像预填充阶段那样进行并行计算；其次，随着生成的进行，KV-Cache会不断增长，导致每一步的计算量都在增加。因此，如何优化解码阶段的效率，特别是KV-Cache 的管理和利用，就成为了提升大语言模型推理性能的关键所在。理解这两个阶段的工作原理和相互关系，对于开发更高效的推理优化技术具有重要的指导意义。

## 8.1.2 缓存复用

在多轮对话和超长上下文场景中，大语言模型往往需要反复处理大量文本内容，其中包含大量重复计算。传统方法每次对话都需要完整重新计算所有历史对话的键值表示（KV-Cache），这造成了显著的资源浪费。针对这一问题，缓存复用技术提供了一种创新的解决方案，通过将历史对话的计算结果存储，实现了计算资源的智能复用。

缓存复用技术的核心思想是将预计会重复使用的内容预先缓存在设备中。具体实现过程可以分为以下几个关键环节：在首次对话时，系统会完整执行标准的推理流程，包括预填充和解码阶段。但与常规处理不同的是，系统会将生成的KV-Cache 完整地保存下来。当用户发起后续对话时，系统会首先加载历史对话对应的 KV-Cache，然后仅对新增的对话内容进行计算。如下图所示，在包含历史对话T0、T1 的基础上新增内容T2 时，系统只需计算T2对应的q2、k2 和v2，而历史部分的键值对则直接从缓存读取。这种技术带来了显著的性能优势。在注意力计算环节，只需要构建一个注意力矩阵下三角部分的新增的梯形部分：新增内容 q2 会与来自缓存的 $\mathrm { k } 0$ 、k1 以及实时计算的 k2 共同计算注意力权重。随后，这些权重会与来自缓存的v0、v1 以及实时计算的 v2 进行矩阵乘法运算，生成新的token 表示。在整个过程中，历史键值对完全避免了重复计算，仅需执行一次缓存读取操作。对话结束后，系统会将更新后的KV-Cache（包含新增内容的计算结果）同步回缓存，为后续对话做好准备。

![](images/601e8939a1ac284c30a6735ba62289b0b481cd445fc1706066e46467c7e64e4d.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个对话模型的处理流程，具体分为第一轮对话和第二轮对话。在第一轮对话中，用户输入T0，模型（LLM）生成响应T1。在第二轮对话中，用户继续输入T2。图片中间部分展示了模型如何处理这些输入和生成响应的过程。可以看到，模型使用了键值对（k0, k1, k2）和查询（q2）来处理输入，并通过一系列操作生成最终的响应v2。
```


缓存复用技术的优势主要体现在两个方面：首先，它显著降低了首Token 延迟（Time toFirst Token），因为系统避免了重复计算历史内容；其次，通过减少计算量，不仅提升了响应速度，还大幅降低了服务运营成本。这种技术特别适合客服系统、持续对话助手等需要长期维护对话上下文的场景，为实现高效、经济的大模型部署提供了重要技术支持。随着上下文窗口的不断扩大，缓存复用技术的价值将愈发凸显。

## 8.1.3 KV-Cache 压缩

在大模型推理过程中，KV-Cache 呈现出显著的幂律分布特征。研究表明，虽然KV-Cache可能占据大量内存/显存空间，但其中真正对模型输出质量产生关键影响的缓存条目仅占少数，这些少数关键缓存（Heavy Hitters, H2）对模型输出的贡献远高于其他冗余缓。通过智能筛选出这些对模型性能至关重要的KV-Cache 条目，并采用优化的牺牲策略进行选择性保留。在有限KV 缓存预算下，动态平衡保留H2 与最近token，实现最小化性能损失。

通过监控注意力分数分布，识别对当前生成步骤影响最大的Key-Value 对，并结合滑动窗口保留最近的 Token，以平衡长程依赖与局部上下文。确定最优的 KV-Cache 牺牲策略本质上是一个复杂的组合优化问题。其核心挑战在于需要预测未来token 对这些缓存条目的依赖程度，使用基于贪心算法的近似求解方案能够很好近似最优解。该方法通过统计分析历史token 的注意力分数分布模式，保留 H2，逐出低权重 token，能够高效地逼近理论最优解，在保证计算效率的同时获得令人满意的优化效果。除了保存H2 这些重要信息，为了避免上下文丢失，强制保留最近生成的token，新token 直接覆盖被逐出的KV，无需显存交换。

### 8.2 智能应用统一门户性能

## 8.2.1 门户实例数量限制

在智能应用统一门户系统中，用户可以创建多个门户，每个门户各对应一个Dify 实例。用户可创建门户的数量主要取决于一体机的性能。

单个Dify 实例在空载状态下的资源消耗如下：

$\mathrm { C P U } \leq 1$ 内存 $\leq 3 \mathrm { G }$ 磁盘 $\leq 1 \mathrm { G }$

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-E-0001</td><td>AI一体机整 体配置</td><td>CPU: Intel Xeon Gold 6430 32 核高 64线程*2 内存：32GB DDR4 RECC *8 硬盘1:480G SATA*2 硬盘2：3.84TNVME*2 GPU: GeForce RTX 4090 24GB *4 电源：2000W*2 Ukey : XT200*3 安全卡：SanSec TDEC（Trusted Data Encryption Card）*1</td><td></td></tr></table></body></html>

以上述硬件配置为例，计算门户用户可创建的最大应用数量：

$\boxed { \overline { { \bullet } } }$ 大模型框架、大模型运维管理系统：4G  
$\boxed { \bullet }$ 数据库：4G  
$\boxed { \overline { { \pmb { \bigtriangledown } } } }$ 知识库加密服务：1G  
$\boxed { \overline { { \bullet } } }$ 建议创建门户数量： $\leq 5$ 个最大创建门户数量：8 个

## 8.2.2 门户使用限制（迭代 2）

以下为门户配置中的默认限制，本期系统部署后无法调整，后期考虑在创建门户时支持通过界面调整

<html><body><table><tr><td></td><td>配置项</td><td>说明</td><td>默认值</td></tr><tr><td>1</td><td>知识库加密开关</td><td>是否开启知识库加密</td><td>是</td></tr><tr><td>2</td><td>应用市场开关</td><td>是否允许通过应用市场安装应用</td><td>是</td></tr><tr><td>3</td><td>应用签名校验开关</td><td>关闭后，将允许安装所有未在官方应用市场上 架审核的插件，可能存在安全隐患</td><td>是</td></tr></table></body></html>

<html><body><table><tr><td>4</td><td>知识库存储类型</td><td>知识库存储类型支持： 本地存储 网络存储（S3、OSS、OBS)</td><td>本地存储</td></tr><tr><td>5</td><td>知识库本地存储路径</td><td>指定知识库本地存储的目录，用于挂载专用的 知识库磁盘</td><td></td></tr><tr><td>6</td><td>知识库上传文件大小</td><td></td><td>20M</td></tr><tr><td>7</td><td>知识库批量上传文件数量</td><td></td><td>5</td></tr><tr><td>8</td><td>上传图片大小</td><td></td><td>10M</td></tr><tr><td>9</td><td>上传音频大小</td><td></td><td>50M</td></tr><tr><td>10</td><td>上传视频大小</td><td></td><td>100M</td></tr><tr><td>11</td><td>邮件服务器配置</td><td>用于用户密码找回、工作流输出结果发送</td><td>无</td></tr><tr><td>12</td><td>文档分段长度配置</td><td>用于控制处理长文本时的分段大小 较大分段：适合上下文依赖性强的任务，例如 情感分析或长文档总结。 较小分段：适合精细分析场景，例如关键词提 取或段落级内容处理。</td><td>4000</td></tr><tr><td>13</td><td>提示词生成上限</td><td>用于控制应用输出到LLM的提示词token上 限；过短的提示词可能导致LLM输出不准， 过长的提示词会增加LLM资源消耗</td><td>512</td></tr></table></body></html>

## 8.2.3 知识库容量限制

知识库中文档的大小将直接影响Dify 实例的资源使用量：

1. 文档原始文件将保存在服务器上，占用磁盘

2. 文档内容被切割后保存在业务数据库中，占用内存、磁盘

3. 文档内容经过嵌入模型处理后保存在向量数据库中，占用内存、磁盘文档大小和资源使用量之间的关系，根据性能测试结果计算如下：

<html><body><table><tr><td>文档大小</td><td>高质量 索引模式</td><td>经济 索引模式</td><td>磁盘使用量</td><td>内存使用量</td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

# 第九章 安全设计

### 9.1 知识库加密

实现对知识库中所有文档原始文件的透明加密，用户操作无感。  
实现对知识库中所有文档原始文件的访问权限控制。

实现方式如下：

1. 在部署流程中自动完成 kms 文件加密插件的安装、启动  
2. 在部署流程中自动完成 kms 服务的安装、启动  
3. 通过脚本完成加密插件的初始化，包括：

(1) 环境变量配置

\#\# 拿到容器 id  
KMS_CONTAINER_ID $\scriptstyle 1 = 9$ \$(docker ps | grep seckms_ai | awk '{print \$1}')  
\#\# TODO 改成要加密的目录  dify 的 docker/app/storage  
ENC_DATEDIR $\scriptstyle = _ { I }$ opt/test

(2) 创建秘钥

docker exec -it \$KMS_CONTAINER_ID sh -c '   
curl --location --request POST '\''http://127.0.0.1:3333/kms/core/v4/keys/create'\'' \   
--header '\''Content-Type: application/json'\'' \   
--data-raw '\''{ "alg": "SM4", "certReplaceUser": true, "keyLength": 128, "keyName": "SM4", "keyType": "SymmetricKey", "keyVersion": 1, "username": "sansec_ai"   
}'\''   
'

(3) 添加本地文件加密配置

docker exec -it \$KMS_CONTAINER_ID sh -c '   
curl --location --request POST '\''http://127.0.0.1:7075/kms/storage/v4/server/local/create'\'' \   
--header '\''Content-Type: application/json'\'' \   
--data-raw '\''{ "systemType": "Linux", "serverIp": "**********", "serverName": "secfs", "serverPort": "52000", "userName": "sansec_ai", "hmacKey": "", "hmacProtectKey": ""

<html><body><table><tr><td>}\"</td></tr><tr><td></td></tr></table></body></html>

(4) 添加加密目录配置

docker exec -it \$KMS_CONTAINER_ID sh -c "   
curl --location --request POST 'http://127.0.0.1:7075/kms/storage/v4/policy/c13y/add' \   
--header 'Content-Type: application/json' \   
--data-raw '{ \"dirPath\": \"\$ENC_DATEDIR\", \"keyAlg\": \"SM4\", \"keyName\": \"SM4\", \"serverIp\": \"**********\", \"serverPort\": \"52000\", \"systemType\": \"Linux\"   
}'   
"

(5) 对目录进行加密

docker exec -it $\$ 123,45$ _CONTAINER_ID sh -c "   
curl --location --request POST 'http://127.0.0.1:7075/kms/storage/v4/policy/c13y/encrypt' \   
--header 'Content-Type: application/json' \   
--data-raw '{ \"dirPath\": \"\$ENC_DATEDIR\", \"keyAlg\": \"SM4\", \"keyName\": \"SM4\", \"serverIp\": \"**********\", \"serverPort\": \"52000\", \"systemType\": \"Linux\", \"dirType\": \"Folder\", \"status\": \"Unencrypted\"   
}'   
"

(6) 添加root 用户对加密目录的权限

docker exec -it \$KMS_CONTAINER_ID sh -c "   
curl --location --request POST 'http://127.0.0.1:7075/kms/storage/v4/policy/access/add' \   
--header 'Content-Type: application/json' \   
--data-raw '{ \"accessName\": \"\", \"accessOtherName\": \"root\", \"accessType\": \"2\", \"dirPath\": \"\$ENC_DATEDIR\", \"permission\": \"3\", \"serverIp\": \"**********\", \"serverPort\": \"52000\", \"systemType\": \"Linux\"   
}'

### 9.2 敏感信息安全传输

1. Web 控制台的管理页面，采用HTTPS 安全传输协议。  
2. C/Java 客户端本地配置文件内密码加密存储，密钥缓存数据采用密文存储。

### 9.3 认证鉴权

1. Web 管控控制台，支持失败次数锁定功能。  
2. 系统管理员用户，业务用户密码必须满足复杂度要求，必须 3 类字符，最小长度 8 个字符串。

### 9.4 协议与接口防攻击

1. 系统对外提供的网络服务端口，限定为必须范围。对外提供端口见使用手册。

### 9.5 产品开发、发布和安装安全

1. 系统数据在 postgresql 数据库中保存，受 Linux 系统和 postgresql 认证机制保护。  
2. 系统设计无隐藏的访问方式。  
3. 系统设计无不可管理的认证/访问方式。  
4. 所有系统参数/命令全部写入使用手册等文档。

### 9.6 Web 系统安全

1. Web 登录页面的口令，采用Hash 值方式存储数据库中。  
2. 系统设计全部采用公开的加密算法，例如：AES，3DES，SM4，RSA，SM2, EDDSA 等。  
3. 系统设计没有采用硬编码的密钥。

### 9.7 系统管理和维护安全

1. 系统的 Linux 用户口令满足口令复杂度要求。  
2. 系统漏洞扫描采用公司指定的扫描工具。

### 9.8 WEB 安全

1. Web 管理页面，需要登录认证后才能访问。只有认证过的用户会话，才能访问web 管理页面。

2. 认证处理过程在KMS 服务端进行。

3. 需要进行漏洞扫描，如果发现问题，及时修复解决。

4. web 应用程序使用Tomcat 容器的会话生成机制生成会话标识。

5. 用户登陆认证通过后更换会话标识，以防止会话固定（session fixation）漏洞。

6. 涉及上传文件的功能，都需要验证文件类型，满足对应功能项要求。

### 9.9 代码扫描和病毒扫描

1． 系统的开发代码，使用公司指定的CPPCheck,Findbugs 代码静态检查工具进行扫描，解决风险为“高”的告警。

2.  产品在发版测试时，进行安全扫描，解决安全扫描报告中级别比较高的问题。

### 9.10 合法监听接口

1、合法监听接口应遵循通用的国际标准。如果客户要求不能提供合法监听接口时，必须从产品和解决方案中删除此接口，确保现网设备无法通过任何形式启用2、系统中和合法监听相关的接口禁止存在合法监听网关正常接口之外的任何其它调用方式，包括命令、脚本、调试接口等。确保产品和解决方案的合法监听接口的安全性，防止被盗用、误用或滥用，防止被监听对象以及任何未经授权的第三方感知，防止相关信息或内容被泄露3、在正常业务流程和标准协议之外，禁止提供可在最终用户不知情同意的情况下，采集最终用户无关业务内容的功能

### 9.11 隐私保护

1. 产品不涉及用户数据的采集。

### 9.12 未公开接口

1. 系统设计方案中，无“未公开接口”。  
2. 系统设计方案中，不存在可绕过系统安全机制（认证、权限控制、日志记录）对系统或数据进行访问的功能

### 9.13 访问通道控制

1、 系统支持业务与管理接口分离，保证系统安全访问。

2、 系统所有的对外通信连接都是系统运行和维护必需的，对使用到的通信端口在产品通信矩阵文档中说明，动态侦听端口必须限定确定的合理的范围。通过端口扫描工具验证，未在通信矩阵中列出的端口必须关闭

3、 所有能对系统进行管理的接口必须有接入认证机制，标准协议没有认证机制的除外。

4、 设备外部可见的能对系统进行管理的物理接口必须有接入认证机制。

### 9.14 软件完整性保护

1、在软件包（含补丁包）发布前，需要经过至少两款防病毒软件扫描，保证防病毒软件不产生告警，特殊情况下对告警作出解释说明。扫描记录（防病毒软件名称、软件版本、病毒库版本、扫描时间、扫描结果等）存档并随软件包（含补丁包）发布给客户。

2、产品对外发布的软件（包含软件包/补丁包）必须提供完整性校验机制，在安装、升级过程中对软件进行完整性验证。KMS 产品的升级包，采用完整性验证和签名验证机制。

### 9.15 敏感数据与加密保护

1、 认证凭据不允许明文存储在系统中，应该加密保护。  
2、 设计方案无私有加密算法。  
3、 用于敏感数据传输加密的密钥，不能硬编码在代码中。

### 9.16 权限最小化

### 9.17 口令要求

1、设置口令时，默认检测口令复杂度：系统默认检测口令复杂度，口令至少满足如下要求：

1）口令长度至少8 个字符；

2）口令必须包含如下至少三种字符的组合:

－至少一个小写字母；  
－至少一个大写字母；  
－至少一个数字；  
－至少一个特殊字符： $\therefore ( \underline { { { \omega } } } \# \$ \$ 9 \% \wedge \& \ : ^ { * } ( ) - = + \backslash | [ \{ \} ] ; ^ { \cdots } , < . > / 2$

# 第十章 稳定性设计

1. 系统日志通过配置策略，限制文件个数，文件大小。  
2. 系统创建的密钥数量，受系统配置参数限制。达到容量上限后，不允许再创建密钥。  
3. 系统设计有健康检查模块，对于服务运行状态进行监控，可以自动重新启动异常关闭的服务模块。

### 第十一章 异常处理

### 11.1 异常信息设计

【说明】捕捉出错信息、分析定位、提示信息。

### 11.2 异常情况处理

【说明】错误处理方案与帮助手段。

# 第十二章 附录与规范

### 12.1 附录 1（组件清单）

<html><body><table><tr><td>NO.</td><td>组件名</td><td>组件层级 （产品平台/技术平 台/框架/模块)</td><td>组件类型 （直接复用/修改后复用/ 新组件)</td><td>备注</td></tr><tr><td>1</td><td>JCE</td><td>模块</td><td>直接复用</td><td></td></tr><tr><td>2</td><td>postgresql</td><td>模块</td><td>直接复用</td><td></td></tr><tr><td>3</td><td>安全大模型运 维管理组件</td><td>模块</td><td>新组件</td><td></td></tr><tr><td></td><td>智能应用门户 组件</td><td>模块</td><td>新组件</td><td></td></tr><tr><td></td><td>KMS+文件加密</td><td>模块</td><td>直接复用</td><td></td></tr><tr><td></td><td>数据围栏</td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

### 12.2 附录 2（端口清单）

<html><body><table><tr><td>NO.</td><td>协议</td><td>端口号</td><td>端口说明</td><td>所属网口(管理/业务)</td><td>所属服务</td><td>备注</td></tr><tr><td>1</td><td>TCP</td><td>9443</td><td>WEB 管理页 面</td><td>管理</td><td>控制台</td><td></td></tr><tr><td>2</td><td>TCP</td><td>1235</td><td>监控检查</td><td>管理</td><td>监控</td><td></td></tr><tr><td>3</td><td>TCP</td><td>8443</td><td>Web管理页 面</td><td>管理</td><td>控制台</td><td></td></tr><tr><td>4</td><td>UDP</td><td>161， 162</td><td>SNMP 通信</td><td>管理</td><td>SNMP</td><td></td></tr><tr><td>5</td><td>TCP</td><td>4567</td><td>数据复制 的通讯和 数据传输 端口</td><td>管理</td><td>集群</td><td></td></tr><tr><td>6</td><td>TCP</td><td>4568</td><td>数据复制 的通讯和 数据传输 端口</td><td>管理</td><td>集群</td><td></td></tr><tr><td>7</td><td>TCP</td><td>4444</td><td>数据复制 的通讯和 数据传输</td><td>管理</td><td>集群</td><td></td></tr></table></body></html>

<html><body><table><tr><td></td><td></td><td></td><td>端口</td><td></td><td></td><td></td></tr><tr><td>8</td><td>TCP</td><td>22</td><td>ssh 端口, 用于远程 连接系统 使用</td><td>管理</td><td>SSH</td><td></td></tr></table></body></html>