#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基础功能测试脚本
测试AI智能测试平台的核心功能
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.config import Config
from src.storage import StorageManager
from src.utils.llm_client import LLMClient
from src.requirement_manager import RequirementManager
from src.test_manager import TestManager
from src.document_manager import DocumentManager

def test_section():
    print("\n" + "=" * 50)
    print("测试章节提取")
    print("=" * 50)
    
    try:
        config = Config()
        llm_client = LLMClient(config)
        storage = StorageManager(config)
        req_manager = RequirementManager(storage, config)
        doc_manager = DocumentManager(storage, config)
        project_id = "56ff9433-a18f-403b-be0a-b52938b2707b"
        document_id = "e7d450d1-6f2f-423f-be1b-1156ecab8c10"
        section_separator = "##"
        sections = doc_manager.get_document_sections(document_id, project_id, section_separator)
        # for section in sections:
        #     print(f"  ---- {section['title']}: {section['content'][:50]}")
        system_prompt = req_manager.load_requirement_prompt()
        requirements = llm_client.extract_requirements(sections[0], system_prompt)
        print(f"✓ 文本需求解析成功: {len(requirements)}个需求")
        
        for req in requirements:
            #print(f"  - {req.get('number', 'N/A')}: {req.get('description', 'N/A')}")
            print(f"  - {req}")

    except Exception as e:
        print(f"✗ 文档解析失败: {e}")
        import traceback
        traceback.print_exc()
        return False
def main():
    """主测试函数"""
    test_results = []
    
    # 运行各项测试
    #test_results.append(("需求功能提取", test_requirement_extract()))
    #test_results.append(("测试用例生成", test_make_case()))
    test_results.append(("文档解析", test_section()))
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
