'需求功能点：\n需求PR-F-6701: 数据脱敏组件需要提供对敏感数据进行识别和处置的能力。\n\n参考知识库内容：\n><td>PR-F-6701</td><td>需求名称</td><td>数据脱敏组件</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">数据脱敏组件需要提供对敏感数据进行识别和处置的能力。</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>输入输出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">不涉及</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>\n\n#### 6.7.2 内容审核组件\n\n<html><body><table><tr><td>需求编号</td><td>PR-F-6702</td><td>需求名称</td><td>内容审核组件</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">内容审核组件需要对涉及政治、暴力、色情等敏感信息的内容进行检测和拦截。</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>输入输出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">不涉及</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>\n\n#### 6.7.3 组件集成\n\n<html><body><table><tr><td>需求编号</td><td>PR-F-6703</td><td>需求名称</td><td>数据脱敏与内容审核组件集成</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="3">1.将数据脱敏组件与内容审核组件集成为数据围栏系统； 2.数据围栏系统以docker形式部署在一体机中； 3.数据围栏系统以restful api的方式提供服务；</td><td></td><td></td></tr><tr><td>业务流程</td><td colspan="3">4.\n\n### 5.1 数据围栏系统简介\n\n数据围栏系统包含数据脱敏与内容审核组件两部分。\n\n数据脱敏用于保障企业的数据安全和隐私安全，提供一系列针对敏感数据的识别和处置方案，其中包括敏感数据识别算法，数据脱敏处理方式，业务自定义的配置选项和海量数据处理能力。数据围栏系统用于实时检测大模型的输出内容是否涉及政治、暴力、色情等敏感信息并对敏感信息进行拦截，满足相关法律法规及企业安全的要求。\n\n一体机集成开源组件 godlp 提供数据脱敏的能力。该组件能够广泛支持结构化（JSON数据、KV 数据、golang map）和非结构化数据（多语言字符串）；内置多种敏感数据识别规则，能对原始数据进行敏感类型识别，确保敏感信息能被妥善处理；支持多种脱敏算法，业务可以根据需求对敏感数据进行不同的脱敏处理；\n\ngodlp 默认支持手机号、邮箱地址、中国身份证号、借记卡号、信用卡号、中国护照、中文地址、MAC 地址、银行分支机构号、中国驾照、比特币钱包地址、域名、IP 地址、姓名、生日、年龄、学历、国籍、经纬度信息、电话号码等内容进行脱敏。除默认的敏感信息识别和处理规则外，业务可以根据实际情况，配置自定义的YAML 规则。\n\n一体机集成开源安全检测工具 ShieldLM 帮助检测大型语言模型生成内容中的安全问题。该工具支持检测多种不安全内容，例如有毒和有偏见的生成内容、对身心有害的内容、非法和不道德的内容、涉及侵犯隐私的内容、可能导致财产损失的内容以及涉及敏感话题的内容。'