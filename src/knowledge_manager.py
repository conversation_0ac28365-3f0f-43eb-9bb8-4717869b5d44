#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import uuid
from typing import Dict, List, Optional
import chromadb
from chromadb.config import Settings
from .storage import StorageManager
from .utils.embedding_client import EmbeddingClient
from .config import Config

class KnowledgeManager:
    """知识库管理器"""
    
    def __init__(self, storage: StorageManager, config:Config):
        self.storage = storage
        self.config = config
        self.embedding_client = EmbeddingClient(config)
        
        # 初始化ChromaDB
        self.chroma_path = os.path.join(config.KNOWLEDGE_BASE_PATH, 'chroma_db')
        os.makedirs(self.chroma_path, exist_ok=True)
        
        self.chroma_client = chromadb.PersistentClient(
            path=self.chroma_path,
            settings=Settings(anonymized_telemetry=False)
        )
    
    def get_or_create_collection(self, project_id: str):
        """获取或创建项目的知识库集合"""
        collection_name = f"project_{project_id}"
        try:
            collection = self.chroma_client.get_collection(collection_name)
        except:
            collection = self.chroma_client.create_collection(
                name=collection_name,
                metadata={"project_id": project_id}
            )
        return collection
    
    def add_document_to_knowledge_base(self, project_id: str, document_id: str, 
                                     content: str, metadata: Dict = None) -> bool:
        """将文档添加到知识库"""
        try:
            print(f"正在导入知识库文档: {metadata.get('name', document_id)}")
            # 分块处理文档内容
            chunks = self._split_text(content)
            
            collection = self.get_or_create_collection(project_id)
            
            # 为每个块生成嵌入向量
            embeddings = []
            documents = []
            metadatas = []
            ids = []
            
            for i, chunk in enumerate(chunks):
                if chunk.strip():  # 跳过空块
                    embedding = self.embedding_client.get_embedding(chunk)
                    embeddings.append(embedding)
                    documents.append(chunk)
                    
                    chunk_metadata = {
                        'document_id': document_id,
                        'chunk_index': i,
                        'chunk_type': 'text'
                    }
                    if metadata:
                        chunk_metadata.update(metadata)
                    metadatas.append(chunk_metadata)
                    
                    ids.append(f"{document_id}_chunk_{i}")
            
            if embeddings:
                # 尝试添加到集合，如果失败则尝试重新创建集合
                try:
                    collection.add(
                        embeddings=embeddings,
                        documents=documents,
                        metadatas=metadatas,
                        ids=ids
                    )
                except Exception as collection_error:
                    print(f"添加到集合失败: {collection_error}")
                    # 如果是compaction错误，尝试重新创建集合
                    if "compaction" in str(collection_error).lower() or "metadata segment" in str(collection_error).lower():
                        try:
                            # 重新创建集合
                            collection_name = f"project_{project_id}"
                            try:
                                self.chroma_client.delete_collection(collection_name)
                            except:
                                pass  # 如果删除失败，继续尝试创建
                            
                            collection = self.chroma_client.create_collection(
                                name=collection_name,
                                metadata={"project_id": project_id}
                            )
                            
                            # 重新添加数据
                            collection.add(
                                embeddings=embeddings,
                                documents=documents,
                                metadatas=metadatas,
                                ids=ids
                            )
                        except Exception as recreate_error:
                            print(f"重新创建集合失败: {recreate_error}")
                            raise collection_error  # 抛出原始错误
                    else:
                        raise collection_error
            
            print(f"   文档已导入知识库: {metadata.get('name', document_id)}")
            return True
            
        except Exception as e:
            print(f"添加文档到知识库失败: {e}")
            return False
    
    def _split_text(self, text: str, use_markdown_separator: bool = True) -> List[str]:
        """分割文本为块"""
        chunk_size = self.config.CHUNK_SIZE
        chunk_overlap = self.config.CHUNK_OVERLAP

        # 获取markdown分隔符配置
        markdown_separator = getattr(self.config, 'MARKDOWN_SEPARATOR', '###')

        if use_markdown_separator and markdown_separator:
            # 尝试按markdown分隔符分块
            markdown_chunks = self._split_by_markdown_separator(text, markdown_separator)
            if markdown_chunks and len(markdown_chunks) > 1:
                # 如果成功按markdown分隔符分块，进一步处理每个块
                final_chunks = []
                for chunk in markdown_chunks:
                    if len(chunk) <= chunk_size:
                        final_chunks.append(chunk)
                    else:
                        # 如果单个markdown块太大，继续按大小分割
                        sub_chunks = self._split_by_size(chunk, chunk_size, chunk_overlap)
                        final_chunks.extend(sub_chunks)
                return final_chunks

        # 如果没有markdown分隔符或分块失败，按大小分割
        return self._split_by_size(text, chunk_size, chunk_overlap)

    def _split_by_markdown_separator(self, text: str, separator: str) -> List[str]:
        """按markdown分隔符分割文本"""
        chunks = []
        lines = text.split('\n')
        current_chunk = []

        # 处理不同类型的分隔符
        if separator.startswith('#'):
            # Markdown标题分隔符
            separator_pattern = separator + ' '
        else:
            # 其他分隔符（如 ---）
            separator_pattern = separator

        for line in lines:
            line_stripped = line.strip()

            # 检查是否是分隔符
            is_separator = False
            if separator.startswith('#'):
                # 对于Markdown标题，检查是否以指定级别的#开头，并且后面跟着空格和章节编号
                if line_stripped.startswith(separator_pattern):
                    # 进一步检查是否有章节编号（如：### 6.4.2. 监控组件功能扩展）
                    remaining_text = line_stripped[len(separator_pattern):].strip()
                    if remaining_text:
                        # 检查是否以数字开头（章节编号）
                        import re
                        # 匹配章节编号模式：数字.数字.数字. 或 数字. 等
                        if re.match(r'^\d+(\.\d+)*\.?\s+', remaining_text):
                            is_separator = True
                        # 如果没有章节编号，但有内容，也认为是章节标题
                        elif remaining_text and not remaining_text.startswith('#'):
                            is_separator = True
            else:
                # 对于其他分隔符，检查是否完全匹配或包含分隔符
                is_separator = (line_stripped == separator or
                               line_stripped.startswith(separator) or
                               separator in line_stripped)

            if is_separator and current_chunk:
                # 保存当前块
                chunk_text = '\n'.join(current_chunk).strip()
                if chunk_text:
                    chunks.append(chunk_text)
                current_chunk = [line]  # 开始新块，包含分隔符行
            else:
                current_chunk.append(line)

        # 保存最后一个块
        if current_chunk:
            chunk_text = '\n'.join(current_chunk).strip()
            if chunk_text:
                chunks.append(chunk_text)

        return chunks

    def _split_by_size(self, text: str, chunk_size: int, chunk_overlap: int) -> List[str]:
        """按大小分割文本"""
        if len(text) <= chunk_size:
            return [text]

        chunks = []
        start = 0

        while start < len(text):
            end = start + chunk_size

            # 尝试在句号、换行符等位置分割
            if end < len(text):
                # 向后查找合适的分割点
                for i in range(min(100, len(text) - end)):
                    if text[end + i] in ['.', '\n', '。', '！', '？']:
                        end = end + i + 1
                        break

            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)

            start = end - chunk_overlap
            if start >= len(text):
                break

        return chunks
    
    def search_knowledge_base(self, project_id: str, query: str, top_k: int = 5) -> List[Dict]:
        """搜索知识库"""
        try:
            collection = self.get_or_create_collection(project_id)
            
            # 获取查询的嵌入向量
            query_embedding = self.embedding_client.get_embedding(query)
            
            # 搜索相似文档
            results = collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                include=['documents', 'metadatas', 'distances']
            )
            
            search_results = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    result = {
                        'content': doc,
                        'metadata': results['metadatas'][0][i] if results['metadatas'] else {},
                        'similarity': 1 - results['distances'][0][i] if results['distances'] else 0
                    }
                    search_results.append(result)
            
            return search_results
            
        except Exception as e:
            print(f"搜索知识库失败: {e}")
            return []
    
    def upload_knowledge_file(self, project_id: str, file, name: str = None, 
                            description: str = "", file_type: str = "knowledge") -> str:
        """上传知识库文件"""
        from .document_manager import DocumentManager
        doc_manager = DocumentManager(self.storage, self.config)
        
        # 上传文件并转换为文档
        document_id = doc_manager.upload_document(project_id, file, name, description)
        
        # 获取文档内容
        content = doc_manager.get_document_content(document_id, project_id)
        
        # 添加到知识库
        metadata = {
            'file_type': file_type,
            'name': name or file.filename,
            'description': description
        }
        
        success = self.add_document_to_knowledge_base(project_id, document_id, content, metadata)
        
        if not success:
            # 如果添加到知识库失败，删除文档
            doc_manager.delete_document(document_id, project_id)
            raise Exception("添加到知识库失败")
        
        return document_id
    
    def remove_document_from_knowledge_base(self, project_id: str, document_id: str) -> bool:
        """从知识库中移除文档"""
        try:
            collection = self.get_or_create_collection(project_id)
            
            # 查找该文档的所有块
            results = collection.get(
                where={"document_id": document_id}
            )

            if results and 'ids' in results and results['ids']:
                collection.delete(ids=results['ids'])
            
            return True
            
        except Exception as e:
            print(f"从知识库移除文档失败: {e}")
            return False
    
    def list_knowledge_documents(self, project_id: str) -> List[Dict]:
        """获取知识库文档列表"""
        try:
            collection = self.get_or_create_collection(project_id)
            
            # 获取所有文档的元数据
            results = collection.get(include=['metadatas'])
            
            # 按文档ID分组
            documents = {}
            for metadata in results['metadatas']:
                doc_id = metadata.get('document_id')
                if doc_id and doc_id not in documents:
                    documents[doc_id] = {
                        'document_id': doc_id,
                        'name': metadata.get('name', ''),
                        'description': metadata.get('description', ''),
                        'file_type': metadata.get('file_type', 'knowledge'),
                        'chunk_count': 0
                    }
                
                if doc_id:
                    documents[doc_id]['chunk_count'] += 1
            
            return list(documents.values())
            
        except Exception as e:
            print(f"获取知识库文档列表失败: {e}")
            return []
    
    def get_knowledge_base_stats(self, project_id: str) -> Dict:
        """获取知识库统计信息"""
        try:
            collection = self.get_or_create_collection(project_id)
            
            # 获取总块数
            total_chunks = collection.count()
            
            # 获取文档数
            results = collection.get(include=['metadatas'])
            document_ids = set()
            file_types = {}
            
            for metadata in results['metadatas']:
                doc_id = metadata.get('document_id')
                if doc_id:
                    document_ids.add(doc_id)
                
                file_type = metadata.get('file_type', 'unknown')
                file_types[file_type] = file_types.get(file_type, 0) + 1
            
            return {
                'total_documents': len(document_ids),
                'total_chunks': total_chunks,
                'file_types': file_types
            }
            
        except Exception as e:
            print(f"获取知识库统计失败: {e}")
            return {
                'total_documents': 0,
                'total_chunks': 0,
                'file_types': {}
            }
    
    def test_knowledge_base(self, project_id: str, test_queries: List[str]) -> List[Dict]:
        """测试知识库检索效果"""
        results = []
        
        for query in test_queries:
            search_results = self.search_knowledge_base(project_id, query, top_k=3)
            
            result = {
                'query': query,
                'results_count': len(search_results),
                'top_results': search_results[:3] if search_results else []
            }
            results.append(result)
        
        return results
    
    def clear_knowledge_base(self, project_id: str) -> bool:
        """清空项目知识库"""
        try:
            collection_name = f"project_{project_id}"
            self.chroma_client.delete_collection(collection_name)
            return True
        except Exception as e:
            print(f"清空知识库失败: {e}")
            return False
    
    def auto_import_documents(self, project_id: str, progress_callback=None) -> Dict:
        """自动导入项目的需求/设计文档到知识库"""
        from .document_manager import DocumentManager
        doc_manager = DocumentManager(self.storage, self.config)
        
        results = {
            'success_count': 0,
            'failed_count': 0,
            'errors': []
        }
        
        try:
            # 获取项目的所有文档
            documents = doc_manager.list_documents(project_id, page_size=1000)['records']
            
            completed_count = 0
            for doc in documents:
                try:
                    content = doc.get('content', '')
                    if content:
                        metadata = {
                            'name': doc.get('name', ''),
                            'description': doc.get('description', ''),
                            'file_type': 'document',
                            'original_filename': doc.get('original_filename', '')
                        }
                        
                        success = self.add_document_to_knowledge_base(
                            project_id, doc['id'], content, metadata
                        )
                        
                        if success:
                            results['success_count'] += 1
                        else:
                            results['failed_count'] += 1
                            results['errors'].append(f"文档 {doc['name']} 导入失败")
                    
                except Exception as e:
                    results['failed_count'] += 1
                    results['errors'].append(f"文档 {doc.get('name', doc['id'])} 导入失败: {str(e)}")
                #  更新进度
                if progress_callback:
                    completed_count += 1
                    progress_callback(100 * completed_count / len(documents))
            
        except Exception as e:
            results['errors'].append(f"获取文档列表失败: {str(e)}")
        
        return results
