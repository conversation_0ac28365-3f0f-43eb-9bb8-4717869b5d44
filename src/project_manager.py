#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from typing import Dict, List, Optional
from .storage import StorageManager

class ProjectManager:
    """项目管理器"""
    
    def __init__(self, storage: StorageManager):
        self.storage = storage
    
    def create_project(self, name: str, description: str = "") -> str:
        """创建项目"""
        data = {
            'name': name,
            'description': description
        }
        
        project_id = self.storage.create('projects', data)
        
        # 创建项目相关的目录结构
        import os
        project_path = os.path.join(self.storage.data_path, 'projects', project_id)
        os.makedirs(project_path, exist_ok=True)
        
        # 创建子目录
        subdirs = ['documents', 'requirements', 'test_cases', 'knowledge_base']
        for subdir in subdirs:
            os.makedirs(os.path.join(project_path, subdir), exist_ok=True)
        
        return project_id
    
    def get_project(self, project_id: str) -> Optional[Dict]:
        """获取项目详情"""
        return self.storage.read('projects', project_id)
    
    def update_project(self, project_id: str, name: str = None, description: str = None) -> bool:
        """更新项目"""
        data = {}
        if name is not None:
            data['name'] = name
        if description is not None:
            data['description'] = description
        
        if not data:
            return False
        
        return self.storage.update('projects', project_id, data)
    
    def delete_project(self, project_id: str) -> bool:
        """删除项目"""
        # 删除项目相关的所有数据
        import shutil
        import os
        
        # 删除项目目录
        project_path = os.path.join(self.storage.data_path, 'projects', project_id)
        if os.path.exists(project_path):
            shutil.rmtree(project_path)
        
        # 删除项目记录
        return self.storage.delete('projects', project_id)
    
    def list_projects(self, page: int = 1, page_size: int = 20, search: str = None) -> Dict:
        """获取项目列表"""
        if search:
            # 搜索项目
            projects = self.storage.search('projects', search, fields=['name', 'description'])
            total = len(projects)
            start = (page - 1) * page_size
            end = start + page_size
            page_projects = projects[start:end]
            
            return {
                'records': page_projects,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size
            }
        else:
            return self.storage.list('projects', page=page, page_size=page_size)
    
    def get_project_stats(self, project_id: str) -> Dict:
        """获取项目统计信息"""
        stats = {
            'documents': 0,
            'requirements': 0,
            'requirements_ai_generated': 0,
            'requirements_ai_modified': 0,
            'test_cases': 0,
            'test_cases_ai_generated': 0,
            'test_cases_ai_modified': 0,
            'automation_test_cases': 0,
            'automation_test_cases_ai_generated': 0,
            'automation_test_cases_ai_modified': 0,
            'knowledge_items': 0
        }

        try:
            # 统计文档数量
            docs_result = self.storage.list('documents', project_id=project_id, page_size=1)
            stats['documents'] = docs_result['total']

            # 统计需求数量和AI生成情况
            reqs_result = self.storage.list('requirements', project_id=project_id, page_size=10000)
            stats['requirements'] = reqs_result['total']
            if reqs_result.get('records'):
                for req in reqs_result['records']:
                    if req.get('ai_generated'):
                        stats['requirements_ai_generated'] += 1
                        if req.get('ai_modified_at'):
                            stats['requirements_ai_modified'] += 1

            # 统计用户测试用例数量和AI生成情况
            tests_result = self.storage.list('test_cases', project_id=project_id, page_size=10000)
            stats['test_cases'] = tests_result['total']
            if tests_result.get('records'):
                for test in tests_result['records']:
                    if test.get('ai_generated'):
                        stats['test_cases_ai_generated'] += 1
                        if test.get('ai_modified_at'):
                            stats['test_cases_ai_modified'] += 1

            # 统计自动化测试用例数量和AI生成情况
            automation_result = self.storage.list('automation_test_cases', project_id=project_id, page_size=10000)
            stats['automation_test_cases'] = automation_result['total']
            if automation_result.get('records'):
                for auto_test in automation_result['records']:
                    if auto_test.get('ai_generated'):
                        stats['automation_test_cases_ai_generated'] += 1
                        if auto_test.get('ai_modified_at'):
                            stats['automation_test_cases_ai_modified'] += 1

        except Exception as e:
            print(f"获取项目统计信息时出错: {e}")

        return stats
    
    def validate_project_name(self, name: str, project_id: str = None) -> Dict:
        """验证项目名称"""
        if not name or not name.strip():
            return {'valid': False, 'message': '项目名称不能为空'}
        
        if len(name.strip()) > 100:
            return {'valid': False, 'message': '项目名称不能超过100个字符'}
        
        # 检查名称是否重复
        projects = self.storage.list('projects', page_size=1000)['records']
        for project in projects:
            if project['name'] == name.strip() and project['id'] != project_id:
                return {'valid': False, 'message': '项目名称已存在'}
        
        return {'valid': True, 'message': '项目名称可用'}
    
    def export_project(self, project_id: str) -> Dict:
        """导出项目数据"""
        project = self.get_project(project_id)
        if not project:
            return None
        
        # 获取项目相关的所有数据
        documents = self.storage.list('documents', project_id=project_id, page_size=1000)['records']
        requirements = self.storage.list('requirements', project_id=project_id, page_size=1000)['records']
        test_cases = self.storage.list('test_cases', project_id=project_id, page_size=1000)['records']
        
        return {
            'project': project,
            'documents': documents,
            'requirements': requirements,
            'test_cases': test_cases
        }
