#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil
from typing import Dict, List, Optional
from werkzeug.utils import secure_filename
from .storage import StorageManager
from .utils.file_converter import FileConverter

class DocumentManager:
    """文档管理器"""
    
    def __init__(self, storage: StorageManager, config):
        self.storage = storage
        self.config = config
        self.upload_folder = config.UPLOAD_FOLDER
        self.converter = FileConverter()
        
        # 确保上传目录存在
        os.makedirs(self.upload_folder, exist_ok=True)
    
    def upload_document(self, project_id: str, file, name: str = None, description: str = "") -> str:
        """上传文档"""
        if not file or not file.filename:
            raise ValueError("没有选择文件")
        
        # 获取安全的文件名
        # filename = secure_filename(file.filename)
        # if not filename:
        #     raise ValueError("文件名无效")
        filename = file.filename
        
        # 检查文件类型
        file_ext = os.path.splitext(filename)[1].lower()
        allowed_extensions = ['.pdf', '.docx', '.doc', '.md', '.txt']
        if file_ext not in allowed_extensions:
            raise ValueError(f"不支持的文件类型: {file_ext}")
        
        # 生成唯一的文件名
        import uuid
        unique_filename = f"{uuid.uuid4().hex}_{filename}"
        file_path = os.path.join(self.upload_folder, unique_filename)
        
        # 保存文件
        file.save(file_path)
        
        try:
            # 转换文件内容为Markdown
            content = self._convert_to_markdown(file_path, file_ext)
            
            # 保存文档记录
            data = {
                'project_id': project_id,
                'name': name or os.path.splitext(filename)[0],
                'description': description,
                'file_path': file_path,
                'original_filename': filename,
                'file_type': file_ext,
                'content': content,
                'file_size': os.path.getsize(file_path)
            }
            
            document_id = self.storage.create('documents', data, project_id)
            return document_id
            
        except Exception as e:
            # 如果处理失败，删除已上传的文件
            if os.path.exists(file_path):
                os.remove(file_path)
            raise e
    
    def _convert_to_markdown(self, file_path: str, file_ext: str) -> str:
        """将文件转换为Markdown格式"""
        try:
            if file_ext == '.md':
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            elif file_ext == '.txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    return f"```\n{content}\n```"
            elif file_ext in ['.docx', '.doc']:
                return self.converter.docx_to_markdown(file_path)
            elif file_ext == '.pdf':
                return self.converter.pdf_to_markdown(file_path)
            else:
                raise ValueError(f"不支持的文件类型: {file_ext}")
        except Exception as e:
            print(f"文件转换失败: {e}")
            return f"# 文件转换失败\n\n错误信息: {str(e)}"
    
    def get_document(self, document_id: str, project_id: str = None) -> Optional[Dict]:
        """获取文档详情"""
        return self.storage.read('documents', document_id, project_id)
    
    def update_document(self, document_id: str, project_id: str = None, 
                       name: str = None, description: str = None) -> bool:
        """更新文档信息"""
        data = {}
        if name is not None:
            data['name'] = name
        if description is not None:
            data['description'] = description
        
        if not data:
            return False
        
        return self.storage.update('documents', document_id, data, project_id)
    
    def delete_document(self, document_id: str, project_id: str = None) -> bool:
        """删除文档"""
        # 获取文档信息
        document = self.get_document(document_id, project_id)
        if not document:
            return False
        
        # 删除文件
        file_path = document.get('file_path')
        if file_path and os.path.exists(file_path):
            try:
                os.remove(file_path)
            except Exception as e:
                print(f"删除文件失败: {e}")
        
        # 删除数据库记录
        return self.storage.delete('documents', document_id, project_id)
    
    def list_documents(self, project_id: str, page: int = 1, page_size: int = 20, 
                      search: str = None) -> Dict:
        """获取文档列表"""
        if search:
            # 搜索文档
            documents = self.storage.search('documents', search, project_id, 
                                          fields=['name', 'description', 'content'])
            total = len(documents)
            start = (page - 1) * page_size
            end = start + page_size
            page_documents = documents[start:end]
            
            return {
                'records': page_documents,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size
            }
        else:
            return self.storage.list('documents', project_id=project_id, 
                                   page=page, page_size=page_size)
    
    def get_document_content(self, document_id: str, project_id: str = None) -> Optional[str]:
        """获取文档内容"""
        document = self.get_document(document_id, project_id)
        if document:
            return document.get('content', '')
        return None
    
    def update_document_content(self, document_id: str, content: str, 
                               project_id: str = None) -> bool:
        """更新文档内容"""
        return self.storage.update('documents', document_id, {'content': content}, project_id)
    
    def get_document_sections(self, document_id: str, project_id: str = None,
                             separator: str = '###') -> List[Dict]:
        """获取文档章节列表（按指定分隔符分隔）"""
        content = self.get_document_content(document_id, project_id)
        if not content:
            return []

        sections = []
        lines = content.split('\n')
        current_content = []

        # 处理不同类型的分隔符
        if separator.startswith('#'):
            # Markdown标题分隔符
            separator_pattern = separator + ' '
        else:
            # 其他分隔符（如 ---）
            separator_pattern = separator

        # 跳过目录
        toc_start = 0
        toc_end = 0
        for line in lines:            
            # 检查是否到目录结束
            if toc_start >1 and line.startswith('#'):
                toc_end = lines.index(line)
                break
            if line.startswith('#') and '目录' in line:
                toc_start = lines.index(line) + 1
        
        current_section = lines[toc_end]
        for line in lines[toc_end:]:
            line_stripped = line.strip()

            # 检查是否是章节分隔符
            is_separator = False
            if separator.startswith('#'):
                # 对于Markdown标题，检查是否以指定级别的#开头，并且后面跟着空格和章节编号
                if line_stripped.startswith(separator_pattern):
                    # 进一步检查是否有章节编号（如：### 6.4.2. 监控组件功能扩展）
                    remaining_text = line_stripped[len(separator_pattern):].strip()
                    if remaining_text:
                        # 检查是否以数字开头（章节编号）
                        import re
                        # 匹配章节编号模式：数字.数字.数字. 或 数字. 等
                        if re.match(r'^\d+(\.\d+)*\.?\s+', remaining_text):
                            is_separator = True
                        # 如果没有章节编号，但有内容，也认为是章节标题
                        elif remaining_text and not remaining_text.startswith('#'):
                            is_separator = True
            else:
                # 对于其他分隔符，检查是否完全匹配或包含分隔符
                is_separator = (line_stripped == separator or
                               line_stripped.startswith(separator) or
                               separator in line_stripped)

            if is_separator:
                # 保存上一个章节
                if current_section:
                    sections.append({
                        'title': current_section,
                        'content': '\n'.join(current_content).strip()
                    })

                # 开始新章节
                current_section = line.strip()
                current_content = []
            else:
                current_content.append(line)

        # 保存最后一个章节
        if current_section:
            sections.append({
                'title': current_section,
                'content': '\n'.join(current_content).strip()
            })

        # 如果没有找到任何章节，将整个文档作为一个章节
        if not sections and content.strip():
            sections.append({
                'title': '文档内容',
                'content': content.strip()
            })

        return sections
    
    def validate_document_name(self, name: str, project_id: str, document_id: str = None) -> Dict:
        """验证文档名称"""
        if not name or not name.strip():
            return {'valid': False, 'message': '文档名称不能为空'}
        
        if len(name.strip()) > 200:
            return {'valid': False, 'message': '文档名称不能超过200个字符'}
        
        # 检查名称是否重复
        documents = self.storage.list('documents', project_id=project_id, page_size=1000)['records']
        for doc in documents:
            if doc['name'] == name.strip() and doc['id'] != document_id:
                return {'valid': False, 'message': '文档名称已存在'}
        
        return {'valid': True, 'message': '文档名称可用'}
