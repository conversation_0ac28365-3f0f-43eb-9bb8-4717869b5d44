#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Blueprint, request, jsonify, session
from ..async_task_manager import get_task_manager, TaskStatus

def create_async_task_blueprint():
    """创建异步任务路由蓝图"""
    bp = Blueprint('async_tasks', __name__, url_prefix='/aitest/api/tasks')
    
    @bp.route('/status/<task_id>')
    def get_task_status(task_id):
        """获取任务状态API"""
        try:
            task_manager = get_task_manager()
            status = task_manager.get_task_status(task_id)
            
            if status:
                return jsonify({
                    'success': True,
                    'data': status
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '任务不存在'
                }), 404
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取任务状态失败: {str(e)}'
            }), 500
    
    @bp.route('/result/<task_id>')
    def get_task_result(task_id):
        """获取任务结果API"""
        try:
            task_manager = get_task_manager()
            status = task_manager.get_task_status(task_id)
            
            if not status:
                return jsonify({
                    'success': False,
                    'message': '任务不存在'
                }), 404
            
            if status['status'] == TaskStatus.COMPLETED.value:
                result = task_manager.get_task_result(task_id)
                return jsonify({
                    'success': True,
                    'data': {
                        'status': status,
                        'result': result
                    }
                })
            else:
                return jsonify({
                    'success': True,
                    'data': {
                        'status': status,
                        'result': None
                    }
                })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取任务结果失败: {str(e)}'
            }), 500
    
    @bp.route('/cancel/<task_id>', methods=['POST'])
    def cancel_task(task_id):
        """取消任务API"""
        try:
            task_manager = get_task_manager()
            success = task_manager.cancel_task(task_id)
            
            if success:
                return jsonify({
                    'success': True,
                    'message': '任务已取消'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '无法取消任务'
                }), 400
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'取消任务失败: {str(e)}'
            }), 500
    
    @bp.route('/list')
    def list_tasks():
        """列出任务API"""
        try:
            task_type = request.args.get('task_type')
            status_str = request.args.get('status')
            
            status = None
            if status_str:
                try:
                    status = TaskStatus(status_str)
                except ValueError:
                    pass
            
            task_manager = get_task_manager()
            tasks = task_manager.list_tasks(task_type=task_type, status=status)
            
            return jsonify({
                'success': True,
                'data': tasks
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取任务列表失败: {str(e)}'
            }), 500
    
    @bp.route('/cleanup', methods=['POST'])
    def cleanup_tasks():
        """清理已完成任务API"""
        try:
            data = request.get_json() or {}
            max_age_hours = data.get('max_age_hours', 24)
            
            task_manager = get_task_manager()
            task_manager.cleanup_completed_tasks(max_age_hours)
            
            return jsonify({
                'success': True,
                'message': '任务清理完成'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'清理任务失败: {str(e)}'
            }), 500
    
    return bp
