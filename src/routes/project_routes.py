#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Blueprint, request, jsonify, render_template, session
from typing import Dict

def create_project_blueprint(project_manager):
    """创建项目路由蓝图"""
    bp = Blueprint('projects', __name__, url_prefix='/aitest/projects')
    
    @bp.route('/')
    def index():
        """项目管理页面"""
        return render_template('projects/index.html')
    
    @bp.route('/api/list')
    def list_projects():
        """获取项目列表API"""
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        search = request.args.get('search', '').strip()
        
        try:
            result = project_manager.list_projects(
                page=page, 
                page_size=page_size, 
                search=search if search else None
            )
            return jsonify({
                'success': True,
                'data': result
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取项目列表失败: {str(e)}'
            }), 500
    
    @bp.route('/api/create', methods=['POST'])
    def create_project():
        """创建项目API"""
        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()
        
        # 验证项目名称
        validation = project_manager.validate_project_name(name)
        if not validation['valid']:
            return jsonify({
                'success': False,
                'message': validation['message']
            }), 400
        
        try:
            project_id = project_manager.create_project(name, description)
            return jsonify({
                'success': True,
                'data': {'id': project_id},
                'message': '项目创建成功'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'创建项目失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<project_id>')
    def get_project(project_id):
        """获取项目详情API"""
        try:
            project = project_manager.get_project(project_id)
            if not project:
                return jsonify({
                    'success': False,
                    'message': '项目不存在'
                }), 404
            
            # 获取项目统计信息
            stats = project_manager.get_project_stats(project_id)
            project['stats'] = stats
            
            return jsonify({
                'success': True,
                'data': project
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取项目详情失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<project_id>/update', methods=['PUT'])
    def update_project(project_id):
        """更新项目API"""
        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()
        
        # 验证项目名称
        if name:
            validation = project_manager.validate_project_name(name, project_id)
            if not validation['valid']:
                return jsonify({
                    'success': False,
                    'message': validation['message']
                }), 400
        
        try:
            success = project_manager.update_project(
                project_id, 
                name=name if name else None,
                description=description if description else None
            )
            
            if success:
                return jsonify({
                    'success': True,
                    'message': '项目更新成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '项目更新失败'
                }), 400
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'更新项目失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<project_id>/delete', methods=['DELETE'])
    def delete_project(project_id):
        """删除项目API"""
        try:
            success = project_manager.delete_project(project_id)
            
            if success:
                # 如果删除的是当前选中的项目，清除session
                if session.get('current_project_id') == project_id:
                    session.pop('current_project_id', None)
                
                return jsonify({
                    'success': True,
                    'message': '项目删除成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '项目删除失败'
                }), 400
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'删除项目失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<project_id>/export')
    def export_project(project_id):
        """导出项目API"""
        try:
            data = project_manager.export_project(project_id)
            if not data:
                return jsonify({
                    'success': False,
                    'message': '项目不存在'
                }), 404
            
            return jsonify({
                'success': True,
                'data': data
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'导出项目失败: {str(e)}'
            }), 500
    
    @bp.route('/api/validate_name', methods=['POST'])
    def validate_project_name():
        """验证项目名称API"""
        data = request.get_json()
        name = data.get('name', '').strip()
        project_id = data.get('project_id')
        
        try:
            validation = project_manager.validate_project_name(name, project_id)
            return jsonify({
                'success': True,
                'data': validation
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'验证项目名称失败: {str(e)}'
            }), 500
    
    return bp
