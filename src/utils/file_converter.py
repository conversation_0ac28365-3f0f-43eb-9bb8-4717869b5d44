#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
from typing import Optional

try:
    from docx import Document
    HAS_DOCX = True
except ImportError:
    HAS_DOCX = False

try:
    import PyPDF2
    HAS_PDF = True
except ImportError:
    HAS_PDF = False

class FileConverter:
    """文件格式转换工具"""
    
    def __init__(self):
        pass
    
    def docx_to_markdown(self, file_path: str) -> str:
        """将Word文档转换为Markdown"""
        if not HAS_DOCX:
            raise Exception("缺少python-docx依赖，无法处理Word文档")

        try:
            doc = Document(file_path)
            markdown_content = []
            
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if not text:
                    markdown_content.append('')
                    continue
                
                # 检查段落样式
                style_name = paragraph.style.name.lower()
                
                if 'heading 1' in style_name or 'title' in style_name:
                    markdown_content.append(f'# {text}')
                elif 'heading 2' in style_name:
                    markdown_content.append(f'## {text}')
                elif 'heading 3' in style_name:
                    markdown_content.append(f'### {text}')
                elif 'heading 4' in style_name:
                    markdown_content.append(f'#### {text}')
                elif 'heading 5' in style_name:
                    markdown_content.append(f'##### {text}')
                elif 'heading 6' in style_name:
                    markdown_content.append(f'###### {text}')
                else:
                    # 普通段落
                    markdown_content.append(text)
                
                markdown_content.append('')  # 添加空行
            
            # 处理表格
            for table in doc.tables:
                markdown_content.append('')
                
                # 表头
                if table.rows:
                    header_row = table.rows[0]
                    headers = [cell.text.strip() for cell in header_row.cells]
                    markdown_content.append('| ' + ' | '.join(headers) + ' |')
                    markdown_content.append('| ' + ' | '.join(['---'] * len(headers)) + ' |')
                    
                    # 数据行
                    for row in table.rows[1:]:
                        cells = [cell.text.strip() for cell in row.cells]
                        markdown_content.append('| ' + ' | '.join(cells) + ' |')
                
                markdown_content.append('')
            
            return '\n'.join(markdown_content)
            
        except Exception as e:
            raise Exception(f"Word文档转换失败: {str(e)}")
    
    def pdf_to_markdown(self, file_path: str) -> str:
        """将PDF文档转换为Markdown"""
        if not HAS_PDF:
            raise Exception("缺少PyPDF2依赖，无法处理PDF文档")

        try:
            markdown_content = []

            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num, page in enumerate(pdf_reader.pages):
                    text = page.extract_text()
                    
                    if text.strip():
                        # 添加页面标题
                        markdown_content.append(f'## 第 {page_num + 1} 页')
                        markdown_content.append('')
                        
                        # 处理文本
                        lines = text.split('\n')
                        processed_lines = []
                        
                        for line in lines:
                            line = line.strip()
                            if not line:
                                continue
                            
                            # 尝试识别标题（简单的启发式方法）
                            if self._is_likely_heading(line):
                                processed_lines.append(f'### {line}')
                            else:
                                processed_lines.append(line)
                        
                        markdown_content.extend(processed_lines)
                        markdown_content.append('')
            
            return '\n'.join(markdown_content)
            
        except Exception as e:
            raise Exception(f"PDF文档转换失败: {str(e)}")
    
    def _is_likely_heading(self, text: str) -> bool:
        """判断文本是否可能是标题"""
        # 简单的启发式规则
        if len(text) > 100:  # 太长的文本不太可能是标题
            return False
        
        # 包含数字编号的可能是标题
        if re.match(r'^\d+\.?\s+', text):
            return True
        
        # 全大写的短文本可能是标题
        if text.isupper() and len(text) < 50:
            return True
        
        # 以特定词汇开头的可能是标题
        heading_keywords = ['第', '章', '节', '部分', '概述', '介绍', '总结', '结论']
        for keyword in heading_keywords:
            if text.startswith(keyword):
                return True
        
        return False
    
    def markdown_to_html(self, markdown_content: str) -> str:
        """将Markdown转换为HTML（用于预览）"""
        try:
            import markdown
            md = markdown.Markdown(extensions=['tables', 'fenced_code', 'toc'])
            html = md.convert(markdown_content)
            
            # 添加基本的CSS样式
            styled_html = f"""
            <div style="max-width: 800px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
                {html}
            </div>
            <style>
                table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                code {{ background-color: #f4f4f4; padding: 2px 4px; border-radius: 3px; }}
                pre {{ background-color: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }}
                blockquote {{ border-left: 4px solid #ddd; margin: 0; padding-left: 20px; color: #666; }}
            </style>
            """
            
            return styled_html
            
        except ImportError:
            # 如果没有markdown库，使用简单的HTML转换
            html = markdown_content.replace('\n', '<br>')
            html = re.sub(r'^# (.+)$', r'<h1>\1</h1>', html, flags=re.MULTILINE)
            html = re.sub(r'^## (.+)$', r'<h2>\1</h2>', html, flags=re.MULTILINE)
            html = re.sub(r'^### (.+)$', r'<h3>\1</h3>', html, flags=re.MULTILINE)
            html = re.sub(r'^#### (.+)$', r'<h4>\1</h4>', html, flags=re.MULTILINE)
            html = re.sub(r'\*\*(.+?)\*\*', r'<strong>\1</strong>', html)
            html = re.sub(r'\*(.+?)\*', r'<em>\1</em>', html)
            
            return f'<div style="max-width: 800px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">{html}</div>'
        
        except Exception as e:
            return f'<div>HTML转换失败: {str(e)}</div>'
    
    def extract_text_from_file(self, file_path: str) -> str:
        """从文件中提取纯文本"""
        file_ext = os.path.splitext(file_path)[1].lower()
        
        try:
            if file_ext == '.txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            elif file_ext == '.md':
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 移除Markdown标记
                    text = re.sub(r'[#*`_\[\]()]', '', content)
                    return text
            elif file_ext in ['.docx', '.doc']:
                doc = Document(file_path)
                text_parts = []
                for paragraph in doc.paragraphs:
                    text_parts.append(paragraph.text)
                return '\n'.join(text_parts)
            elif file_ext == '.pdf':
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    text_parts = []
                    for page in pdf_reader.pages:
                        text_parts.append(page.extract_text())
                    return '\n'.join(text_parts)
            else:
                raise ValueError(f"不支持的文件类型: {file_ext}")
                
        except Exception as e:
            raise Exception(f"文本提取失败: {str(e)}")
