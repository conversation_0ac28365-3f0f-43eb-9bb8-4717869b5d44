import threading
import time
import math  # 用于token估算
import requests
import re
import json
import config
from typing import List, Dict, Optional
from ..config import Config

# 全局限流状态变量（线程安全）
_rate_limit_lock = threading.Lock()
qpm_counter = 0
tpm_counter = 0
API_QPM = 60
API_TPM = 300000
window_start_time = time.time()
def call_LLM(config: Config, messages: List[Dict], temperature: float = 0.7, 
                       max_tokens: int = 8000):
    global qpm_counter, tpm_counter, window_start_time, _rate_limit_lock  # 使用全局限流变量    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {config.OPENAI_API_KEY}"
    }
    stream = True
    data = {
        "model": config.OPENAI_MODEL,
        "messages": messages,
        "temperature": temperature,
        "stream": stream,
        "enable_thinking": True
    }

    # 新增限流逻辑（线程安全）
    input_token_count = len(json.dumps(data))  # 粗略估算=字节长度

    with _rate_limit_lock:
        current_time = time.time()
        # 重置窗口
        if current_time - window_start_time >= 60:
            qpm_counter = 0
            tpm_counter = 0
            window_start_time = current_time

        # QPM/TPM等待逻辑
        while qpm_counter >= API_QPM or tpm_counter + input_token_count > API_TPM:
            sleep_time = max(60 - (current_time - window_start_time) + 0.1, 0)
            print(f"[线程{threading.current_thread().name}] 达到限流阈值，等待{sleep_time:.1f}秒...")
            _rate_limit_lock.release()  # 释放锁让其他线程可以检查
            time.sleep(sleep_time)
            _rate_limit_lock.acquire()  # 重新获取锁
            current_time = time.time()
            if current_time - window_start_time >= 60:
                qpm_counter = 0
                tpm_counter = 0
                window_start_time = current_time

        # 更新计数器
        qpm_counter += 1
        tpm_counter += input_token_count
    
    endpoint_url = config.OPENAI_API_BASE + "/chat/completions"
    content = ""
    with requests.post(endpoint_url, headers=headers, json=data, stream=stream) as response:
        try:
            response.raise_for_status()
        except requests.exceptions.HTTPError as e:
            # 打印错误状态码和响应体
            print(f"HTTP Error {response.status_code}: {e}")
            print(f"Response Body: {response.text[:100]}")
            raise 
        for line in response.iter_lines():
            if line:
                decoded_line = line.decode('utf-8')
                if decoded_line.startswith("data: "):
                    try:
                        chunk = json.loads(decoded_line[6:])  # 去除"data: "前缀并解析
                        delta_content = chunk["choices"][0]["delta"].get("content", "")
                        if delta_content:                            
                            content += delta_content
                    except json.JSONDecodeError:
                        continue
    
    # 输出token估算（线程安全）
    output_token_count = math.ceil(len(content) * 1.5)
    with _rate_limit_lock:
        tpm_counter += output_token_count
    
    return content

def extract_json_from_content(content):
    """
    从大模型返回的 content 字符串中提取并解析 JSON 代码块
    """
    # 用正则表达式提取 ```json ... ``` 之间的内容
    # print(f"解析json: {content}")
    match = re.search(r"``json\s*(.*?)\s*```", content, re.DOTALL)
    if match:
        json_str = match.group(1)
        try:
            # 解析为 Python 对象
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            print("JSON解析失败：", e)
            return None
    else:
        try:
            if '</think>' in content:
                content = content.split('</think>')[-1]
            content = content.replace('```json','').replace('```json','')
            content= content.strip()
            content_json = json.loads(content)
            return content_json
        except:
            pass
        print(f"未找到JsoN代码块：{content[:100]}({len(content)})")
        return None

def remove_think( text):
        """
        移除字符串中所有<ai>和</ai>标签及其之间的内容。

        参数:
            text (str): 输入的字符串

        返回:
            str: 处理后的字符串
        """
        pattern = r'<think>.*?</think>'
        while True:
            new_text = re.sub(pattern, '', text, flags=re.DOTALL)
            if new_text == text:
                break
            text = new_text
        if all(c in ('\n', ' ') for c in text):
            text = text.replace('\n', '').replace(' ', '')
        return 