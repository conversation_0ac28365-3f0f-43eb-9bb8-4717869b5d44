#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import openai
from typing import List
import time

class EmbeddingClient:
    """嵌入向量客户端"""
    
    def __init__(self, config):
        self.config = config
        self.client = openai.OpenAI(
            api_key=config.EMBEDDING_API_KEY,
            base_url=config.EMBEDDING_API_BASE
        )
        self.model = config.EMBEDDING_MODEL
    
    def get_embedding(self, text: str) -> List[float]:
        """获取文本的嵌入向量"""
        try:
            # 清理文本
            text = text.replace('\n', ' ').strip()
            if not text:
                return [0.0] * 1536  # 返回默认维度的零向量
            
            response = self.client.embeddings.create(
                model=self.model,
                input=text
            )
            
            return response.data[0].embedding
            
        except Exception as e:
            print(f"获取嵌入向量失败: {e}")
            # 返回默认维度的零向量
            return [0.0] * 1536
    
    def get_embeddings_batch(self, texts: List[str]) -> List[List[float]]:
        """批量获取文本的嵌入向量"""
        embeddings = []
        
        # 分批处理，避免单次请求过大
        batch_size = 100
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            
            try:
                # 清理文本
                cleaned_texts = []
                for text in batch_texts:
                    cleaned_text = text.replace('\n', ' ').strip()
                    if not cleaned_text:
                        cleaned_text = " "  # 空文本用空格代替
                    cleaned_texts.append(cleaned_text)
                
                response = self.client.embeddings.create(
                    model=self.model,
                    input=cleaned_texts
                )
                
                batch_embeddings = [data.embedding for data in response.data]
                embeddings.extend(batch_embeddings)
                
                # 添加延迟避免频率限制
                if i + batch_size < len(texts):
                    time.sleep(0.1)
                
            except Exception as e:
                print(f"批量获取嵌入向量失败: {e}")
                # 为失败的批次添加零向量
                for _ in batch_texts:
                    embeddings.append([0.0] * 1536)
        
        return embeddings
    
    def calculate_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """计算两个嵌入向量的余弦相似度"""
        try:
            import numpy as np
            
            # 转换为numpy数组
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)
            
            # 计算余弦相似度
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
            
        except Exception as e:
            print(f"计算相似度失败: {e}")
            return 0.0
    
    def find_most_similar(self, query_embedding: List[float], 
                         candidate_embeddings: List[List[float]], 
                         top_k: int = 5) -> List[int]:
        """找到最相似的候选向量的索引"""
        similarities = []
        
        for i, candidate in enumerate(candidate_embeddings):
            similarity = self.calculate_similarity(query_embedding, candidate)
            similarities.append((similarity, i))
        
        # 按相似度降序排序
        similarities.sort(reverse=True)
        
        # 返回前top_k个索引
        return [idx for _, idx in similarities[:top_k]]
    
    def test_embedding_service(self) -> any:
        """测试嵌入服务是否正常"""
        test_text = "这是一个测试文本"
        
        try:
            start_time = time.time()
            embedding = self.get_embedding(test_text)
            end_time = time.time()
            
            return {
                'success': True,
                'response_time': end_time - start_time,
                'embedding_dimension': len(embedding),
                'message': '嵌入服务正常'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': '嵌入服务异常'
            }
